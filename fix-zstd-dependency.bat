@echo off
echo Fixing zstd-jni dependency issue...

REM Create a backup directory for the corrupted files
mkdir zstd-backup

REM Copy the working version to replace the corrupted one
echo Copying working version to replace corrupted one...
xcopy /E /I /Y F:\repository\com\github\luben\zstd-jni\1.5.5-10\* F:\repository\com\github\luben\zstd-jni\1.5.5-11\

REM Rename the files to match the expected version
cd F:\repository\com\github\luben\zstd-jni\1.5.5-11
ren zstd-jni-1.5.5-10.jar zstd-jni-1.5.5-11.jar
ren zstd-jni-1.5.5-10.pom zstd-jni-1.5.5-11.pom

echo Done! Now try running your application again.
