package com.lirong.framework.common.util.retry;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.function.Predicate;

/**
 * 重试工具类，支持指数退避策略
 */
@Slf4j
public class RetryUtil {

    /**
     * 使用指数退避策略进行重试
     *
     * @param callable 要执行的操作
     * @param retryPredicate 判断是否需要重试的条件
     * @param maxRetries 最大重试次数
     * @param initialBackoffMillis 初始退避时间（毫秒）
     * @param maxBackoffMillis 最大退避时间（毫秒）
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，则抛出最后一次异常
     */
    public static <T> T retryWithExponentialBackoff(
            Callable<T> callable,
            Predicate<Exception> retryPredicate,
            int maxRetries,
            long initialBackoffMillis,
            long maxBackoffMillis) throws Exception {
        
        Exception lastException = null;
        long backoffMillis = initialBackoffMillis;
        
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    log.info("Retry attempt {} after {} ms", attempt, backoffMillis);
                }
                return callable.call();
            } catch (Exception e) {
                lastException = e;
                
                // 判断是否需要重试
                if (!retryPredicate.test(e) || attempt >= maxRetries) {
                    throw e;
                }
                
                // 等待退避时间
                try {
                    Thread.sleep(backoffMillis);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw e;
                }
                
                // 计算下一次退避时间（指数增长，但不超过最大值）
                backoffMillis = Math.min(backoffMillis * 2, maxBackoffMillis);
            }
        }
        
        // 不应该到达这里，但为了编译器满意
        throw lastException;
    }
}
