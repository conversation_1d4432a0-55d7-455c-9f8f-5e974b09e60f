package com.lirong.module.doc.enums;

import com.lirong.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {

    // ========== 文献分类表，支持多层分类  ==========
    ErrorCode CATEGORIES_NOT_EXISTS = new ErrorCode(20001, "文献分类表，支持多层分类不存在");
    ErrorCode CATEGORIES_EXITS_CHILDREN = new ErrorCode(20002, "存在存在子文献分类表，支持多层分类，无法删除");
    ErrorCode CATEGORIES_PARENT_NOT_EXITS = new ErrorCode(20003,"父级文献分类表，支持多层分类不存在");
    ErrorCode CATEGORIES_PARENT_ERROR = new ErrorCode(20004, "不能设置自己为父文献分类表，支持多层分类");
    ErrorCode CATEGORIES_NAME_DUPLICATE = new ErrorCode(20005, "已经存在该分类名称的文献分类表，支持多层分类");
    ErrorCode CATEGORIES_PARENT_IS_CHILD = new ErrorCode(20006, "不能设置自己的子Categories为父Categories");

    ErrorCode INSTITUTIONS_NOT_EXISTS = new ErrorCode(20011, "机构表，存储文献发布机构的信息不存在");

    // ========== 研究领域 ==========
    ErrorCode RESEARCH_FIELDS_NOT_EXISTS = new ErrorCode(20021, "研究领域不存在");

    // ========== 机构研究领域  ==========
    ErrorCode INSTITUTION_RESEARCH_FIELDS_NOT_EXISTS = new ErrorCode(20031, "机构研究领域不存在");

    // ========== 文献 ==========
    ErrorCode DOCUMENTS_NOT_EXISTS = new ErrorCode(20041, "文献不存在");
    ErrorCode DOCUMENT_PUBLISHER_NOT_EXISTS = new ErrorCode(20042, "文献发布机构不存在");

    // ========== 专家  ==========
    ErrorCode EXPERTS_NOT_EXISTS = new ErrorCode(20051, "专家不存在");

    ErrorCode DOCUMENT_EXPERTS_NOT_EXISTS = new ErrorCode(20061, "文献作者不存在");

    ErrorCode DOCUMENT_IMPORT_NOT_EXISTS = new ErrorCode(20071, "导入文件不存在");
}

