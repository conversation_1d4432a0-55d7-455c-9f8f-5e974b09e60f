package com.lirong.module.doc.dal.mysql.institutions;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.doc.controller.admin.researchfields.vo.ResearchFieldsPageReqVO;
import com.lirong.module.doc.dal.dataobject.institutions.InstitutionsDO;
import com.lirong.module.doc.dal.dataobject.researchfields.ResearchFieldsDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.doc.controller.admin.institutions.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 机构表，存储文献发布机构的信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstitutionsMapper extends BaseMapperX<InstitutionsDO> {

    List<InstitutionsDO> selectInstitutionList(@Param("reqVO") InstitutionsPageReqVO reqVO);

    IPage<InstitutionsDO> selectList(IPage<InstitutionsDO> page, @Param("reqVO") InstitutionsPageReqVO reqVO);

    @Select("SELECT * FROM doc_institutions WHERE name = #{name} LIMIT 1")
    InstitutionsDO selectByName(String name);

}