package com.lirong.module.doc.task;

import com.lirong.module.doc.controller.admin.documents.vo.DocumentsPageReqVO;
import com.lirong.module.doc.dal.dataobject.documents.DocumentsDO;
import com.lirong.module.doc.service.documents.DocumentsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class FindUnSyncTask  implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(FindUnSyncTask.class);

    @Lazy
    @Resource
    private DocumentsService documentsService;

    @Lazy
    @Resource
    private SyncDoc2EsTask syncDoc2EsTask;

    private static final int BATCH_SIZE = 1000; // 每批处理1000条
    private volatile boolean isSyncing = false;

    @Override
    public void run(String... args) {
        manualSync(); // 启动时自动执行
    }

    /**
     * 手动触发同步任务
     * 查找未同步的文档并添加到同步队列
     */
    public synchronized void manualSync() {
        if (isSyncing) {
            log.warn("同步任务正在进行中，请稍后再试");
            return;
        }

        log.info("开始查找未同步文档");
        isSyncing = true;
        int totalProcessed = 0;

        try {
            int pageNo = 1;
            while (true) {
                try {
                    DocumentsPageReqVO reqVO = new DocumentsPageReqVO();
                    reqVO.setPageNo(pageNo);
                    reqVO.setPageSize(BATCH_SIZE);
                    reqVO.setSyncStatus(0);
                    reqVO.setSynchronize(true);

                    List<DocumentsDO> batch = documentsService.getDocumentsPage(reqVO).getList();
                    if (batch.isEmpty()) {
                        log.info("未同步文档查找完毕，共处理 {} 条数据", totalProcessed);
                        break;
                    }

                    log.info("处理第 {} 批数据，共 {} 条", pageNo, batch.size());
                    for (DocumentsDO record : batch) {
                        if (record != null && record.getId() != null) {
                            syncDoc2EsTask.addDataToQueue(record);
                            totalProcessed++;
                        } else {
                            log.warn("跳过无效文档记录，数据可能不完整");
                        }
                    }
                    pageNo++;
                } catch (Exception e) {
                    log.error("处理第 {} 批数据时发生异常", pageNo, e);
                    // 继续处理下一批，不中断整个同步过程
                    pageNo++;
                }
            }
        } catch (Exception e) {
            log.error("同步任务发生异常", e);
        } finally {
            isSyncing = false;
            log.info("同步任务完成，共添加 {} 条文档到同步队列", totalProcessed);
        }
    }

}
