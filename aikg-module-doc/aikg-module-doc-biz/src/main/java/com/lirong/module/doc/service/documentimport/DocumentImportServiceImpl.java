package com.lirong.module.doc.service.documentimport;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lirong.framework.common.util.retry.RetryUtil;
import com.lirong.module.infra.api.file.FileApi;
import io.minio.errors.ErrorResponseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


import com.lirong.module.doc.controller.admin.documentimport.vo.*;
import com.lirong.module.doc.dal.dataobject.documentimport.DocumentImportDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.doc.dal.mysql.documentimport.DocumentImportMapper;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.Callable;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.doc.enums.ErrorCodeConstants.*;

/**
 * 文档导入记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DocumentImportServiceImpl implements DocumentImportService {

    @Resource
    private DocumentImportMapper documentImportMapper;
    @Resource
    private FileApi fileApi;


    @Override
    public Long createDocumentImport(DocumentImportSaveReqVO createReqVO) {
        // 插入
        DocumentImportDO documentImport = BeanUtils.toBean(createReqVO, DocumentImportDO.class);
        documentImportMapper.insert(documentImport);
        // 返回
        return documentImport.getId();
    }

    @Override
    public void updateDocumentImport(DocumentImportSaveReqVO updateReqVO) {
        // 校验存在
        validateDocumentImportExists(updateReqVO.getId());
        // 更新
        DocumentImportDO updateObj = BeanUtils.toBean(updateReqVO, DocumentImportDO.class);
        documentImportMapper.updateById(updateObj);
    }

    @Override
    public void deleteDocumentImport(Long id) {
        // 校验存在
        validateDocumentImportExists(id);
        // 删除
        documentImportMapper.deleteById(id);
    }

    private void validateDocumentImportExists(Long id) {
        if (documentImportMapper.selectById(id) == null) {
            throw exception(DOCUMENTS_NOT_EXISTS);
        }
    }

    @Override
    public DocumentImportDO getDocumentImport(Long id) {
        return documentImportMapper.selectById(id);
    }

    @Override
    public PageResult<DocumentImportDO> getDocumentImportPage(DocumentImportPageReqVO pageReqVO) {
        // 创建分页对象
        IPage<DocumentImportDO> page = new Page(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 执行分页查询
        IPage<DocumentImportDO> result = documentImportMapper.selectPage(page, pageReqVO);

        // 转换为自定义的 PageResult
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    public boolean uploadFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        try {
            // 读取文件内容（只读取一次，避免多次读取导致流关闭）
            byte[] fileContent = file.getBytes();
            String path = "import/" + UUID.fastUUID() + "." + FileUtil.getSuffix(fileName);

            // 使用重试机制上传文件
            String filePath = uploadFileWithRetry(fileName, path, fileContent);

            // 保存导入记录
            DocumentImportDO importDO = new DocumentImportDO();
            importDO.setFileName(fileName);
            importDO.setFilePath(filePath);
            importDO.setStatus((short)-1);
            documentImportMapper.insert(importDO);

            return true;
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用重试机制上传文件
     *
     * @param fileName 文件名
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件URL
     * @throws Exception 如果上传失败
     */
    private String uploadFileWithRetry(String fileName, String path, byte[] content) throws Exception {
        // 定义重试参数
        final int maxRetries = 5; // 最大重试次数
        final long initialBackoffMillis = 2000; // 初始退避时间（2秒）
        final long maxBackoffMillis = 30000; // 最大退避时间（30秒）

        log.info("开始上传文件: {}, 路径: {}, 大小: {} 字节", fileName, path, content.length);

        // 定义重试条件：只有当异常是MinIO的资源不可写错误时才重试
        try {
            return RetryUtil.retryWithExponentialBackoff(
                    // 要执行的操作
                    (Callable<String>) () -> {
                        try {
                            log.debug("尝试上传文件: {}", path);
                            return fileApi.createFile(fileName, path, content);
                        } catch (Exception e) {
                            log.warn("文件上传失败: {}, 错误: {}", path, e.getMessage());
                            throw e;
                        }
                    },
                    // 判断是否需要重试的条件
                    (Exception e) -> {
                        // 检查是否是MinIO的资源不可写错误
                        Throwable cause = e;
                        while (cause != null) {
                            if (cause instanceof ErrorResponseException) {
                                String message = cause.getMessage();
                                if (message != null && message.contains("Resource requested is unwritable")) {
                                    log.warn("MinIO资源不可写，将进行重试: {}", message);
                                    return true; // 需要重试
                                }
                            }
                            cause = cause.getCause();
                        }
                        log.error("文件上传失败，不符合重试条件: {}", e.getMessage());
                        return false; // 不需要重试
                    },
                    maxRetries,
                    initialBackoffMillis,
                    maxBackoffMillis
            );
        } catch (Exception e) {
            log.error("文件上传失败，重试{}次后仍然失败: {}", maxRetries, e.getMessage());
            throw e;
        }
    }

    public List<DocumentImportDO> getUnParsingRecords() {
        return documentImportMapper.selectList(new QueryWrapper<DocumentImportDO>().eq("status", 0));
    }

}