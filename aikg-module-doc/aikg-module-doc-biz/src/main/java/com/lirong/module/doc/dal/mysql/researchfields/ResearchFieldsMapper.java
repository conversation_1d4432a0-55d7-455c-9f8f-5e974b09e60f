package com.lirong.module.doc.dal.mysql.researchfields;

import java.util.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.doc.controller.admin.categories.vo.CategoriesListReqVO;
import com.lirong.module.doc.dal.dataobject.categories.CategoriesDO;
import com.lirong.module.doc.dal.dataobject.researchfields.ResearchFieldsDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.doc.controller.admin.researchfields.vo.*;

/**
 * 研究领域 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ResearchFieldsMapper extends BaseMapperX<ResearchFieldsDO> {

    default List<ResearchFieldsDO> selectList(ResearchFieldsPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ResearchFieldsDO>()
                .likeIfPresent(ResearchFieldsDO::getChineseName, reqVO.getChineseName())
                .likeIfPresent(ResearchFieldsDO::getEnglishName, reqVO.getEnglishName())
                .betweenIfPresent(ResearchFieldsDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(ResearchFieldsDO::getCreateTime));
    }

    default PageResult<ResearchFieldsDO> selectPage(ResearchFieldsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ResearchFieldsDO>()
                .likeIfPresent(ResearchFieldsDO::getChineseName, reqVO.getChineseName())
                .likeIfPresent(ResearchFieldsDO::getEnglishName, reqVO.getEnglishName())
                .betweenIfPresent(ResearchFieldsDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(ResearchFieldsDO::getCreateTime));
    }

}