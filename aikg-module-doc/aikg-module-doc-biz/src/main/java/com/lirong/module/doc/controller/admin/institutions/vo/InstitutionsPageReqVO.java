package com.lirong.module.doc.controller.admin.institutions.vo;

import com.lirong.module.doc.controller.admin.institutionresearchfields.vo.InstitutionResearchFieldsRespVO;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lirong.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.lirong.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 机构表，存储文献发布机构的信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstitutionsPageReqVO extends PageParam {

    @Schema(description = "机构所在国家")
    private String country;

    @Schema(description = "机构简称")
    private String abbreviation;

    @Schema(description = "机构中文名称", example = "张三")
    private String chineseName;

    @Schema(description = "机构名称", example = "王五")
    private String name;

    @Schema(description = "机构徽章URL", example = "https://www.iocoder.cn")
    private String badgeUrl;

    @Schema(description = "机构简介", example = "你说的对")
    private String description;

    @Schema(description = "网站类型", example = "2")
    private String websiteType;

    @Schema(description = "机构官网")
    private String officialWebsite;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private Long researchFieldId;

}