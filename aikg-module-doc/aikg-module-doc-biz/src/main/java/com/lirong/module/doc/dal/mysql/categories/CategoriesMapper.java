package com.lirong.module.doc.dal.mysql.categories;

import java.util.*;

import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.doc.dal.dataobject.categories.CategoriesDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.doc.controller.admin.categories.vo.*;
import org.apache.ibatis.annotations.Select;

/**
 * 文献分类表，支持多层分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CategoriesMapper extends BaseMapperX<CategoriesDO> {

    default List<CategoriesDO> selectList(CategoriesListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CategoriesDO>()
                .likeIfPresent(CategoriesDO::getName, reqVO.getName())
                .eqIfPresent(CategoriesDO::getParentId, reqVO.getParentId())
                .betweenIfPresent(CategoriesDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CategoriesDO::getId));
    }

	default CategoriesDO selectByParentIdAndName(Long parentId, String name) {
	    return selectOne(CategoriesDO::getParentId, parentId, CategoriesDO::getName, name);
	}

    default Long selectCountByParentId(Long parentId) {
        return selectCount(CategoriesDO::getParentId, parentId);
    }

    @Select("SELECT id, name FROM doc_categories WHERE id NOT IN (SELECT DISTINCT parent_id FROM doc_categories WHERE parent_id IS NOT NULL) AND deleted = 0")
    List<CategoriesDO> selectLeafCategories();
}