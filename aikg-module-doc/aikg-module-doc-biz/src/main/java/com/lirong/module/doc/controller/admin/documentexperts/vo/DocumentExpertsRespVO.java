package com.lirong.module.doc.controller.admin.documentexperts.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 文献作者 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DocumentExpertsRespVO {

    @Schema(description = "文献ID，外键关联doc_documents表", requiredMode = Schema.RequiredMode.REQUIRED, example = "23637")
    @ExcelProperty("文献ID，外键关联doc_documents表")
    private Long documentId;

    @Schema(description = "专家ID，外键关联doc_experts表", requiredMode = Schema.RequiredMode.REQUIRED, example = "29607")
    @ExcelProperty("专家ID，外键关联doc_experts表")
    private Long expertId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}