package com.lirong.module.doc.service.elasticsearch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class EsDocInfo {

    @JsonSerialize(using = StringSerializer.class)
    private Long id;

    @JsonSerialize(using = StringSerializer.class)
    private String rawId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "原始标题")
    private String rawTitle;

    @Schema(description = "关键词")
    private String[] keyWords;

    @Schema(description = "缩略图")
    private String thumbnail;

    @Schema(description = "发布者")
    private String publisher;

    private String[] author;

    @Schema(description = "发布日期")
    private String publishDate;

    private Integer publishYear;

    private Integer publishMonth;

    private Integer publishDay;

    @Schema(description = "文件路径")
    private String fileUrl;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "原始摘要")
    private String rawSummary;


    @Schema(description = "行业标签")
    private String industryLabel;

    @Schema(description = "文献类型")
    private String articleType;

    @Schema(description = "文献类型名称")
    private String articleTypeName;



    @Schema(description = "内容类型")
    private String contentType;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    private String fileContent;

    private String categoryName;

    /**
     * 字数
     */
    private Integer wordCount;
    /**
     * 页数
     */
    private Integer pageCount;


}
