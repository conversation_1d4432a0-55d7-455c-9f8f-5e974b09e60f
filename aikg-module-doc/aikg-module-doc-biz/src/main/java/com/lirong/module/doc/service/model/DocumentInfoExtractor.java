package com.lirong.module.doc.service.model;

import java.util.List;

public class DocumentInfoExtractor {

    private final ModelService modelService;

    public DocumentInfoExtractor() {
        this.modelService = new ModelService();
    }

    /**
     * 翻译文本
     * @param text
     * @return
     */
    public String translateText(String text) {
        String prompt = "请将以下文本翻译为中文：\n" + text + "\n\n"
                + "直接返回翻译结果，不需要Markdown格式\n";
        return modelService.translate(prompt);
    }

    /**
     * 根据文本内容进行分类
     * @param text
     * @param classificationList
     * @return
     */
    public String classify(String text, List<String> classificationList) {
        String classification = "国防政策、地缘政治与军事、国际军事关系、军备控制与裁军、威慑理论与实践、军事战略、国家安全、维和行动、联合作战、战争理论、战役学、战术学、信息战、认知域作战、电子战、特种作战、非对称作战、混合战争、城市作战、反恐作战、海上作战、陆战、电磁频谱作战、网络战、太空战、空中作战、军事训练、军队建设、军队编制体制、军事领导与指挥、军队人力资源、军事法规与条令、军事信息系统、人工智能军事应用、无人作战系统、核生化武器与防护、武器装备、新概念武器、国防科技与工业、军事测绘与气象、战场管理、军事运输与投送、军事工程、军事医学与卫生、情报与侦察、后勤保障、军事思想史、未来战争形态、战史战例研究、军事历史、其他";
        if (classificationList != null && !classificationList.isEmpty()) {
            classification = String.join("、", classificationList);
        }

        String prompt = "请根据文本内容从以下分类中选择一个分类：\n" + classification + "\n\n"
                + "文本内容为：" + text + "\n\n"
                + "直接返回分类结果，不需要Markdown格式\n";

        return modelService.classify(prompt);
    }

    /**
     * 根据提供的文本内容抽取文档信息
     * @param text
     * @return
     */
    public DocumentInfo extractInfoFromText(String text) {
        // 构建提示词
        String prompt = "从以下文本中提取标题、作者、发布机构、摘要、日期和文章所用的语言：\n" + text + "\n\n" +
                "结合文章内容从下面的分类中给文章选择一个分类：\n" +
                "国防政策、地缘政治与军事、国际军事关系、军备控制与裁军、威慑理论与实践、军事战略、国家安全、维和行动、联合作战、战争理论、战役学、战术学、信息战、认知域作战、电子战、特种作战、非对称作战、混合战争、城市作战、反恐作战、海上作战、陆战、电磁频谱作战、网络战、太空战、空中作战、军事训练、军队建设、军队编制体制、军事领导与指挥、军队人力资源、军事法规与条令、军事信息系统、人工智能军事应用、无人作战系统、核生化武器与防护、武器装备、新概念武器、国防科技与工业、军事测绘与气象、战场管理、军事运输与投送、军事工程、军事医学与卫生、情报与侦察、后勤保障、军事思想史、未来战争形态、战史战例研究、军事历史、其他\n" +
                "请按照以下格式返回结果，不需要Markdown格式：\n" +
                "原始标题: <原始标题>\n" +
                "中文标题: <中文标题>\n" +
                "发布机构: <发布机构>\n" +
                "作者: <作者>\n" +
                "原始摘要: <原始摘要>\n" +
                "中文摘要: <中文摘要>\n" +
                "关键词: <文章关键词>\n" +
                "语言: <语言>\n" +
                "分类: <推荐分类>\n" +
                "日期: <日期>\n\n" +
                "注意：\n" +
                "1. 如果日期为完整日期，请转换为yyyy-MM-dd格式。\n" +
                "2. 如果日期为年月格式，请转换为yyyy-MM格式。\n" +
                "3. 如果日期只有年份，请转换为yyyy格式。\n" +
                "请确保日期格式符合以上要求。";

//        System.out.println(prompt);
//        System.out.println("======================\n\n\n\n");
        // 调用模型生成结果
        String extractedInfo = modelService.extractInfo(prompt);

//        System.out.println(extractedInfo);
//        System.out.println("======================\n\n\n\n");
        // 解析结果并填充到实体类
        return parseExtractedInfo(extractedInfo);
    }

    /**
     * 解析抽取结果
     * @param extractedInfo
     * @return
     */
    private DocumentInfo parseExtractedInfo(String extractedInfo) {
        DocumentInfo documentInfo = new DocumentInfo();

        // 解析模型返回的结果
        String[] lines = extractedInfo.replaceAll("\\*\\*", "").split("\n");
        for (String line : lines) {
            if (line.startsWith("原始标题:")) {
                documentInfo.setTitle(line.replace("原始标题:", "").trim());
            } else if (line.startsWith("中文标题:")) {
                documentInfo.setTitleCn(line.replace("中文标题:", "").trim());
            } else if (line.startsWith("原始摘要:")) {
                documentInfo.setSummary(line.replace("原始摘要:", "").trim());
            } else if (line.startsWith("中文摘要:")) {
                documentInfo.setSummaryCn(line.replace("中文摘要:", "").trim());
            } else if (line.startsWith("日期:")) {
                documentInfo.setDate(line.replace("日期:", "").trim());
            } else if (line.startsWith("发布机构:")) {
                documentInfo.setPublisher(line.replace("发布机构:", "").trim());
            } else if (line.startsWith("作者:")) {
                documentInfo.setAuthor(line.replace("作者:", "").trim());
            } else if (line.startsWith("语言:")) {
                documentInfo.setLanguage(line.replace("语言:", "").trim());
            } else if (line.startsWith("分类:")) {
                documentInfo.setCategory(line.replace("分类:", "").trim());
            } else if (line.startsWith("关键词:")) {
                documentInfo.setTags(line.replace("关键词:", "").trim());
            }
        }

        return documentInfo;
    }
}
