package com.lirong.module.doc.controller.admin.documents;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.lirong.framework.common.pojo.PageParam;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.pojo.CommonResult;
import com.lirong.framework.common.util.object.BeanUtils;

import static com.lirong.framework.common.pojo.CommonResult.success;

import com.lirong.framework.excel.core.util.ExcelUtils;

import com.lirong.framework.apilog.core.annotation.ApiAccessLog;
import static com.lirong.framework.apilog.core.enums.OperateTypeEnum.*;

import com.lirong.module.doc.controller.admin.documents.vo.*;
import com.lirong.module.doc.dal.dataobject.documents.DocumentsDO;
import com.lirong.module.doc.service.documents.DocumentsService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "管理后台 - 文献")
@RestController
@RequestMapping("/doc/documents")
@Validated
public class DocumentsController {

    @Resource
    private DocumentsService documentsService;

    @PostMapping("/create")
    @Operation(summary = "创建文献")
    @PreAuthorize("@ss.hasPermission('doc:documents:create')")
    public CommonResult<Long> createDocuments(@Valid @RequestBody DocumentsSaveReqVO createReqVO) {
        return success(documentsService.createDocuments(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新文献")
    @PreAuthorize("@ss.hasPermission('doc:documents:update')")
    public CommonResult<Boolean> updateDocuments(@Valid @RequestBody DocumentsSaveReqVO updateReqVO) {
        documentsService.updateDocuments(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文献")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('doc:documents:delete')")
    public CommonResult<Boolean> deleteDocuments(@RequestParam("id") Long id) {
        documentsService.deleteDocuments(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得文献")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('doc:documents:query')")
    public CommonResult<DocumentsRespVO> getDocuments(@RequestParam("id") Long id) {
        DocumentsDO document = documentsService.getDocuments(id);
        return success(BeanUtils.toBean(document, DocumentsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得文献分页")
    @PreAuthorize("@ss.hasPermission('doc:documents:query')")
    public CommonResult<PageResult<DocumentsRespVO>> getDocumentsPage(@Valid DocumentsPageReqVO pageReqVO) {
        PageResult<DocumentsDO> pageResult = documentsService.getDocumentsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DocumentsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出文献 Excel")
    @PreAuthorize("@ss.hasPermission('doc:documents:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDocumentsExcel(@Valid DocumentsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DocumentsDO> list = documentsService.getDocumentsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "文献.xls", "数据", DocumentsRespVO.class,
                        BeanUtils.toBean(list, DocumentsRespVO.class));
    }

    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "模式一：后端上传文件")
    public CommonResult<DocumentsRespVO> uploadFile(MultipartFile file) throws Exception {
        return success(documentsService.uploadAndParse(file));
    }

    @PostMapping("/create-from-file")
    @Operation(summary = "创建文献")
    @PreAuthorize("@ss.hasPermission('doc:documents:create')")
    public CommonResult<Long> createDocumentFromFile(@Valid @RequestBody DocumentsSaveReqVO createReqVO) {
        return success(documentsService.createDocumentFromFile(createReqVO));
    }

    @PostMapping("/import")
    @Operation(summary = "导入文献列表")
    public CommonResult<DocumentImportRespVO> importDocumentExcel(MultipartFile file) throws IOException {
        List<DocumentImportExcelVO> documentList = ExcelUtils.read(file, DocumentImportExcelVO.class);
        return success(documentsService.importDocumentExcel(documentList));
    }

    @PostMapping("/reparse")
    @Operation(summary = "重新解析文件")
    public CommonResult<Boolean> reparseFileDate() {
        return success(true);
    }

}