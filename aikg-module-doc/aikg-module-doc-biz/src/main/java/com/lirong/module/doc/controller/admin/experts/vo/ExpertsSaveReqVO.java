package com.lirong.module.doc.controller.admin.experts.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 专家表新增/修改 Request VO")
@Data
public class ExpertsSaveReqVO {

    @Schema(description = "专家ID，主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22859")
    private Long id;

    @Schema(description = "专家姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "专家姓名不能为空")
    private String name;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "头像URL", example = "https://www.iocoder.cn")
    private String avatarUrl;

    @Schema(description = "专家简介", example = "你猜")
    private String description;

    @Schema(description = "擅长领域，使用数组存储")
    private String[] expertiseFields;

    @Schema(description = "所属机构ID，外键关联doc_institutions表", requiredMode = Schema.RequiredMode.REQUIRED, example = "10349")
    @NotNull(message = "所属机构ID，外键关联doc_institutions表不能为空")
    private Long institutionId;

    @Schema(description = "职务")
    private String title;

    @Schema(description = "数据源", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.baidu.com")
    @NotEmpty(message = "数据源不能为空")
    private String source;

}