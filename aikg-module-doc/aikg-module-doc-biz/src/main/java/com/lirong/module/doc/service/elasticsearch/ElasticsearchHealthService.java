package com.lirong.module.doc.service.elasticsearch;

import org.elasticsearch.action.admin.cluster.health.ClusterHealthRequest;
import org.elasticsearch.action.admin.cluster.health.ClusterHealthResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.cluster.health.ClusterHealthStatus;
import org.elasticsearch.core.TimeValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static com.lirong.framework.common.consts.AigcConsts.AIGC_ES_INDEX_NAME;

/**
 * Elasticsearch健康检查服务
 * 负责监控ES集群状态和索引健康
 */
@Service
@ConditionalOnProperty(value = "spring.elasticsearch.enabled", havingValue = "true", matchIfMissing = false)
public class ElasticsearchHealthService {

    private static final Logger log = LoggerFactory.getLogger(ElasticsearchHealthService.class);

    private static final TimeValue HEALTH_CHECK_TIMEOUT = TimeValue.timeValueSeconds(5);

    // 初始化时假设 ES 不可用，直到第一次检查成功

    // 使用原子布尔值记录ES是否可用
    private final AtomicBoolean esAvailable = new AtomicBoolean(false);

    // 记录最后一次检查时间
    private final AtomicLong lastCheckTime = new AtomicLong(0);

    @Autowired(required = false)
    private RestHighLevelClient restHighLevelClient;

    @Autowired(required = false)
    private ElasticsearchInitService elasticsearchInitService;

    /**
     * 定时检查ES集群健康状态
     * 每5分钟执行一次
     */
    @Scheduled(initialDelay = 60000, fixedRate = 300000) // 启动后延迟1分钟再执行第一次检查
    public void checkElasticsearchHealth() {
        try {
            // 首先直接检查索引是否存在
            boolean indexExists = checkIndexExists();
            if (!indexExists) {
                log.warn("Elasticsearch索引{}不存在，尝试创建索引", AIGC_ES_INDEX_NAME);
                if (elasticsearchInitService != null) {
                    try {
                        elasticsearchInitService.initElasticsearch();
                        log.info("已尝试创建索引{}", AIGC_ES_INDEX_NAME);
                        // 重新检查索引是否存在
                        indexExists = checkIndexExists();
                    } catch (Exception e) {
                        log.error("创建索引{}失败", AIGC_ES_INDEX_NAME, e);
                    }
                }
            }

            ClusterHealthResponse healthResponse = checkClusterHealth();

            if (healthResponse != null) {
                ClusterHealthStatus status = healthResponse.getStatus();

                if (status == ClusterHealthStatus.RED) {
                    log.error("Elasticsearch集群状态为RED，可能存在严重问题，需要检查ES集群");
                    esAvailable.set(false);
                } else {
                    // 如果直接检查到索引存在，则使用该结果
                    // 否则使用健康检查响应中的索引信息
                    if (indexExists) {
                        log.info("Elasticsearch索引{}存在，集群状态正常，状态: {}", AIGC_ES_INDEX_NAME, status.name());
                        esAvailable.set(true);
                    } else {
                        boolean indexHealthOk = checkIndexHealth(healthResponse);
                        if (!indexHealthOk) {
                            log.warn("Elasticsearch索引{}健康检查失败", AIGC_ES_INDEX_NAME);
                            esAvailable.set(false);
                        } else {
                            log.info("Elasticsearch集群状态正常，状态: {}", status.name());
                            esAvailable.set(true);
                        }
                    }
                }
            } else {
                log.error("无法获取Elasticsearch集群健康状态，可能连接异常");
                esAvailable.set(false);
            }
        } catch (Exception e) {
            log.error("检查Elasticsearch健康状态时发生异常", e);
            esAvailable.set(false);
        }
    }

    /**
     * 检查集群健康状态
     *
     * @return 集群健康响应
     */
    public ClusterHealthResponse checkClusterHealth() {
        if (restHighLevelClient == null) {
            log.warn("RestHighLevelClient不可用，无法检查集群健康状态");
            return null;
        }

        try {
            ClusterHealthRequest request = new ClusterHealthRequest();
            request.timeout(HEALTH_CHECK_TIMEOUT);
            // 设置level为indices，以获取索引级别的健康信息
            request.level(ClusterHealthRequest.Level.INDICES);

            return restHighLevelClient.cluster().health(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("获取Elasticsearch集群健康状态失败", e);
            return null;
        }
    }

    /**
     * 检查索引健康状态
     *
     * @param healthResponse 集群健康响应
     * @return 索引是否存在且健康
     */
    private boolean checkIndexHealth(ClusterHealthResponse healthResponse) {
        // 检查响应中是否包含索引信息
        if (healthResponse.getIndices() == null || healthResponse.getIndices().isEmpty()) {
            log.warn("响应中不包含索引信息，请检查是否正确设置了level参数");
            return false;
        }

        log.debug("检查到的索引列表: {}", healthResponse.getIndices().keySet());

        if (healthResponse.getIndices().containsKey(AIGC_ES_INDEX_NAME)) {
            ClusterHealthStatus indexStatus = healthResponse.getIndices().get(AIGC_ES_INDEX_NAME).getStatus();

            if (indexStatus == ClusterHealthStatus.RED) {
                log.error("Elasticsearch索引{}状态为RED，可能存在严重问题", AIGC_ES_INDEX_NAME);
                return false;
            } else if (indexStatus == ClusterHealthStatus.YELLOW) {
                log.warn("Elasticsearch索引{}状态为YELLOW，可能存在副本分配问题", AIGC_ES_INDEX_NAME);
                return true;
            } else {
                log.info("Elasticsearch索引{}状态正常", AIGC_ES_INDEX_NAME);
                return true;
            }
        } else {
            log.warn("Elasticsearch索引{}不存在，需要创建索引", AIGC_ES_INDEX_NAME);
            return false;
        }
    }

    /**
     * 判断ES是否可用
     *
     * @return ES是否可用
     */
    public boolean isEsAvailable() {
        // 如果restHighLevelClient为空，直接返回false
        if (restHighLevelClient == null) {
            return false;
        }

        // 如果之前未检查过或者上次检查已超过30秒，则重新检查
        long now = System.currentTimeMillis();
        long lastCheck = lastCheckTime.get();
        if (!esAvailable.get() && (now - lastCheck > 30000)) {
            try {
                // 首先直接检查索引是否存在
                boolean indexExists = checkIndexExists();
                if (indexExists) {
                    log.debug("Elasticsearch索引{}存在", AIGC_ES_INDEX_NAME);
                    esAvailable.set(true);
                    lastCheckTime.set(now);
                    return true;
                }

                // 如果索引不存在，则检查集群健康状态
                ClusterHealthRequest request = new ClusterHealthRequest();
                request.timeout(TimeValue.timeValueSeconds(2));
                // 设置level为indices，以获取索引级别的健康信息
                request.level(ClusterHealthRequest.Level.INDICES);
                ClusterHealthResponse response = restHighLevelClient.cluster().health(request, RequestOptions.DEFAULT);
                if (response != null && response.getStatus() != ClusterHealthStatus.RED) {
                    // 如果集群状态正常，则尝试创建索引
                    if (elasticsearchInitService != null && !indexExists) {
                        try {
                            log.info("尝试创建索引{}", AIGC_ES_INDEX_NAME);
                            elasticsearchInitService.initElasticsearch();
                            // 重新检查索引是否存在
                            indexExists = checkIndexExists();
                            if (indexExists) {
                                log.info("索引{}创建成功", AIGC_ES_INDEX_NAME);
                                esAvailable.set(true);
                            }
                        } catch (Exception e) {
                            log.error("创建索引{}失败", AIGC_ES_INDEX_NAME, e);
                        }
                    } else {
                        // 如果集群状态正常，但索引不存在，且无法创建，则设置为不可用
                        esAvailable.set(false);
                    }
                } else {
                    esAvailable.set(false);
                }
            } catch (Exception e) {
                // 忽略异常，保持当前状态
                log.debug("快速检查ES可用性失败", e);
            } finally {
                lastCheckTime.set(now);
            }
        }
        return esAvailable.get();
    }

    /**
     * 直接检查索引是否存在
     *
     * @return 索引是否存在
     */
    private boolean checkIndexExists() {
        if (restHighLevelClient == null) {
            log.warn("RestHighLevelClient不可用，无法检查索引是否存在");
            return false;
        }

        try {
            GetIndexRequest request = new GetIndexRequest(AIGC_ES_INDEX_NAME);
            return restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("检查索引{}是否存在时发生异常", AIGC_ES_INDEX_NAME, e);
            return false;
        }
    }

    /**
     * 等待ES可用
     *
     * @param timeoutSeconds 超时时间（秒）
     * @return ES是否可用
     */
    public boolean waitForEsAvailable(int timeoutSeconds) {
        if (isEsAvailable()) {
            return true;
        }

        long startTime = System.currentTimeMillis();
        long timeoutMillis = TimeUnit.SECONDS.toMillis(timeoutSeconds);

        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            try {
                checkElasticsearchHealth();
                if (esAvailable.get()) {
                    return true;
                }
                // 等待一段时间再重试
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }

        return false;
    }
}
