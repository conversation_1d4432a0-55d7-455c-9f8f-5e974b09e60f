package com.lirong.module.doc.dal.dataobject.documents;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.lirong.framework.mybatis.core.dataobject.BaseDO;
import org.apache.ibatis.type.ArrayTypeHandler;

import java.util.List;

/**
 * 文献 DO
 *
 * <AUTHOR>
 */
@TableName("doc_documents")
@KeySequence("doc_documents_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentsDO extends BaseDO {

    /**
     * 文献ID，主键
     */
    @TableId
    private Long id;
    /**
     * 外文标题
     */
    private String foreignTitle;
    /**
     * 中文标题
     */
    private String chineseTitle;
    /**
     * 发布年份
     */
    private Integer publishYear;
    /**
     * 发布月份
     */
    private Integer publishMonth;
    /**
     * 发布日
     */
    private Integer publishDay;
    /**
     * 外文摘要
     */
    private String foreignAbstract;
    /**
     * 中文摘要
     */
    private String chineseAbstract;
    /**
     * 标签，使用数组存储
     */
    @TableField(typeHandler = ArrayTypeHandler.class)
    private String[] tags;
    /**
     * 文献分类ID，外键关联doc_categories表
     *
     */
    private Long categoryId;
    /**
     * 缩略图URL
     */
    private String thumbnailUrl;
    /**
     * 字数
     */
    private Integer wordCount;
    /**
     * 页数
     */
    private Integer pageCount;
    /**
     * 语种
     */
    private String language;
    /**
     * 原始文献访问地址
     */
    private String originalUrl;
    /**
     * 译文访问地址
     */
    private String translationUrl;
    /**
     * 数据来源
     */
    private String dataSource;
    /**
     * 发布机构ID，外键关联doc_institutions表
     *
     */
    private Long institutionId;

    private Short reviewStatus;

    private Short publishStatus;

    @TableField(exist = false)
    private String institutionName;
    @TableField(exist = false)
    private String categoryName;
    @TableField(exist = false)
    private List<Long> authorIds;
    @TableField(exist = false)
    private List<String> authors;

    private Short syncStatus;

}