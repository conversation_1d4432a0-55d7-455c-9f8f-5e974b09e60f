package com.lirong.module.doc.controller.admin.categories.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 文献分类表，支持多层分类新增/修改 Request VO")
@Data
public class CategoriesSaveReqVO {

    private Long id;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "分类名称不能为空")
    private String name;

    @Schema(description = "父分类ID，用于实现多层分类", example = "4086")
    private Long parentId;

    @Schema(description = "关键词", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关键词不能为空")
    private String keywords;

}