package com.lirong.module.doc.controller.admin.researchfields;

import com.lirong.module.doc.controller.admin.categories.vo.CategoriesListReqVO;
import com.lirong.module.doc.controller.admin.categories.vo.CategoriesRespVO;
import com.lirong.module.doc.dal.dataobject.categories.CategoriesDO;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.lirong.framework.common.pojo.PageParam;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.pojo.CommonResult;
import com.lirong.framework.common.util.object.BeanUtils;
import static com.lirong.framework.common.pojo.CommonResult.success;

import com.lirong.framework.excel.core.util.ExcelUtils;

import com.lirong.framework.apilog.core.annotation.ApiAccessLog;
import static com.lirong.framework.apilog.core.enums.OperateTypeEnum.*;

import com.lirong.module.doc.controller.admin.researchfields.vo.*;
import com.lirong.module.doc.dal.dataobject.researchfields.ResearchFieldsDO;
import com.lirong.module.doc.service.researchfields.ResearchFieldsService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "管理后台 - 研究领域")
@RestController
@RequestMapping("/doc/research-fields")
@Validated
public class ResearchFieldsController {

    @Resource
    private ResearchFieldsService researchFieldsService;

    @PostMapping("/create")
    @Operation(summary = "创建研究领域")
    @PreAuthorize("@ss.hasPermission('doc:research-fields:create')")
    public CommonResult<Long> createResearchFields(@Valid @RequestBody ResearchFieldsSaveReqVO createReqVO) {
        return success(researchFieldsService.createResearchFields(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新研究领域")
    @PreAuthorize("@ss.hasPermission('doc:research-fields:update')")
    public CommonResult<Boolean> updateResearchFields(@Valid @RequestBody ResearchFieldsSaveReqVO updateReqVO) {
        researchFieldsService.updateResearchFields(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除研究领域")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('doc:research-fields:delete')")
    public CommonResult<Boolean> deleteResearchFields(@RequestParam("id") Long id) {
        researchFieldsService.deleteResearchFields(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得研究领域")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('doc:research-fields:query')")
    public CommonResult<ResearchFieldsRespVO> getResearchFields(@RequestParam("id") Long id) {
        ResearchFieldsDO researchFields = researchFieldsService.getResearchFields(id);
        return success(BeanUtils.toBean(researchFields, ResearchFieldsRespVO.class));
    }

    @GetMapping(value = {"/list-all-simple", "/simple-list"})
    @Operation(summary = "获取文献分类精简信息列表", description = "主要用于前端的下拉选项")
    public CommonResult<List<ResearchFieldsRespVO>> getSimpleDeptList() {
        List<ResearchFieldsDO> list = researchFieldsService.getResearchFieldsList(
                new ResearchFieldsPageReqVO());
        return success(BeanUtils.toBean(list, ResearchFieldsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得研究领域分页")
    @PreAuthorize("@ss.hasPermission('doc:research-fields:query')")
    public CommonResult<PageResult<ResearchFieldsRespVO>> getResearchFieldsPage(@Valid ResearchFieldsPageReqVO pageReqVO) {
        PageResult<ResearchFieldsDO> pageResult = researchFieldsService.getResearchFieldsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ResearchFieldsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出研究领域 Excel")
    @PreAuthorize("@ss.hasPermission('doc:research-fields:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportResearchFieldsExcel(@Valid ResearchFieldsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ResearchFieldsDO> list = researchFieldsService.getResearchFieldsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "研究领域.xls", "数据", ResearchFieldsRespVO.class,
                        BeanUtils.toBean(list, ResearchFieldsRespVO.class));
    }

}