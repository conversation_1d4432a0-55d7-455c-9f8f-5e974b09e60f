package com.lirong.module.doc.service.institutionresearchfields;

import com.lirong.module.doc.controller.admin.institutionresearchfields.vo.InstitutionResearchFieldsPageReqVO;
import com.lirong.module.doc.controller.admin.institutionresearchfields.vo.InstitutionResearchFieldsSaveReqVO;
import com.lirong.module.doc.dal.dataobject.institutionresearchfields.InstitutionResearchFieldsDO;
import com.lirong.framework.common.pojo.PageResult;

import javax.validation.Valid;

/**
 * 机构研究领域 Service 接口
 *
 * <AUTHOR>
 */
public interface InstitutionResearchFieldsService {

    /**
     * 创建机构研究领域
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    InstitutionResearchFieldsDO createInstitutionResearchFields(@Valid InstitutionResearchFieldsSaveReqVO createReqVO);

    /**
     * 删除机构研究领域
     *
     */
    void deleteInstitutionResearchFields(Long institutionId, Long researchFieldId);


    /**
     * 获得机构研究领域分页
     *
     * @param pageReqVO 分页查询
     * @return 机构研究领域分页
     */
    PageResult<InstitutionResearchFieldsDO> getInstitutionResearchFieldsPage(InstitutionResearchFieldsPageReqVO pageReqVO);

}