package com.lirong.module.doc.controller.admin.researchfields.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;


@Schema(description = "管理后台 - 研究领域新增/修改 Request VO")
@Data
public class ResearchFieldsSaveReqVO {

    @Schema(description = "研究领域ID，主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1849")
    private Long id;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "中文名称不能为空")
    private String chineseName;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "英文名称不能为空")
    private String englishName;

}