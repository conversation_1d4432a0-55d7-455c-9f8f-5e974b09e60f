package com.lirong;

import org.opencv.core.Core;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import org.opencv.imgcodecs.Imgcodecs;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;

public class OCRExample {
    static {
        System.loadLibrary(Core.NATIVE_LIBRARY_NAME);
        System.load("E:\\software\\opencv\\build\\java\\x64\\opencv_java4100.dll");
    }

    public static void main(String[] args) {
        // 图片路径
        String imagePath = "H://123.png";
        // 指定识别区域
        int x = 0;
        int y = 66;
        int width = 600;
        int height = 934; // 1000 - 66

        // 读取图片
        Mat image = Imgcodecs.imread(imagePath);

        // 裁剪指定区域
        Rect rect = new Rect(x, y, width, height);
        Mat croppedImage = new Mat(image, rect);

        // 将Mat转换为BufferedImage
        BufferedImage bufferedImage = matToBufferedImage(croppedImage);

        // 初始化Tesseract实例
        Tesseract tesseract = new Tesseract();
        tesseract.setDatapath("H:/tessdata"); // 设置tessdata目录路径
        tesseract.setLanguage("chi_sim"); // 设置识别语言
        tesseract.setTessVariable("user_defined_dpi", "300");
//        tesseract.setPageSegMode(PageSegMode.PSM_AUTO);


        try {
            // 识别文字
            String text = tesseract.doOCR(bufferedImage);
            System.out.println("Recognized text: " + text);
        } catch (TesseractException e) {
            System.err.println(e.getMessage());
        }
    }

    // 将OpenCV的Mat对象转换为BufferedImage
    public static BufferedImage matToBufferedImage(Mat mat) {
        int type = BufferedImage.TYPE_3BYTE_BGR;
//        if (mat.channels() > 1) {
//            type = BufferedImage.TYPE_3BYTE_BGR;
//        }
        int bufferSize = mat.channels() * mat.cols() * mat.rows();
        byte[] buffer = new byte[bufferSize];
        mat.get(0, 0, buffer); // get all the pixels
        BufferedImage image = new BufferedImage(mat.cols(), mat.rows(), type);
        final byte[] targetPixels = ((DataBufferByte) image.getRaster().getDataBuffer()).getData();
        System.arraycopy(buffer, 0, targetPixels, 0, buffer.length);
        return image;
    }
}

