package com.lirong;

import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Locale;

public class DateTest {


    // 定义可能的日期格式
    private static final String[] DATE_FORMATS = {
            "MMMM yyyy",       // November 2023
            "MMMM dd, yyyy",   // July 17, 2024
            "yyyy-MM-dd",      // 2023-11-01
            "yyyy-MM",         // 2023-11
            "yyyy"             // 2023
    };

    // 自动判断输入格式并转换为合适的输出格式
    public static String convertDate(String inputDate) {
        for (String format : DATE_FORMATS) {
            try {
                if (format.equals("MMMM yyyy")) {
                    // 处理只有年月的情况（如 November 2023）
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);
                    YearMonth yearMonth = YearMonth.parse(inputDate, inputFormatter);
                    return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                } else if (format.equals("yyyy")) {
                    // 处理只有年的情况（如 2023）
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);
                    Year year = Year.parse(inputDate, inputFormatter);
                    return year.format(DateTimeFormatter.ofPattern("yyyy"));
                } else if (format.equals("yyyy-MM")) {
                    // 处理只有年月的情况（如 2023-11）
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);
                    YearMonth yearMonth = YearMonth.parse(inputDate, inputFormatter);
                    return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                } else {
                    // 处理完整日期的情况（如 July 17, 2024 或 2023-11-01）
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);
                    LocalDate date = LocalDate.parse(inputDate, inputFormatter);
                    return date.format(DateTimeFormatter.ofPattern(getOutputFormat(format)));
                }
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        throw new IllegalArgumentException("无法解析日期: " + inputDate);
    }

    // 根据输入格式决定输出格式
    private static String getOutputFormat(String inputFormat) {
        switch (inputFormat) {
            case "MMMM yyyy":
                return "yyyy-MM"; // November 2023 -> 2023-11
            case "MMMM dd, yyyy":
                return "yyyy-MM-dd"; // July 17, 2024 -> 2024-07-17
            case "yyyy-MM-dd":
                return "yyyy-MM-dd"; // 2023-11-01 -> 2023-11-01
            case "yyyy-MM":
                return "yyyy-MM"; // 2023-11 -> 2023-11
            case "yyyy":
                return "yyyy"; // 2023 -> 2023
            default:
                throw new IllegalArgumentException("不支持的日期格式: " + inputFormat);
        }
    }

    public static void main(String[] args) {
        // 测试日期转换
        String date1 = "November 2023";
        String date2 = "July 17, 2024";
        String date3 = "2023-11-01";
        String date4 = "2023-11";
        String date5 = "2023";

        System.out.println(convertDate(date1));  // 输出: 2023-11
        System.out.println(convertDate(date2));  // 输出: 2024-07-17
        System.out.println(convertDate(date3));  // 输出: 2023-11-01
        System.out.println(convertDate(date4));  // 输出: 2023-11
        System.out.println(convertDate(date5));  // 输出: 2023
    }
}
