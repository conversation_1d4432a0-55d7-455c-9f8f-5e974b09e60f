package com.lirong;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import com.jcraft.jsch.*;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.dialect.PostgreSqlDialect;
import com.jfinal.plugin.druid.DruidPlugin;
import com.lirong.domain.Documents;
import com.lirong.domain.Institutions;
import com.lirong.domain.OriginalDocument;
import com.lirong.framework.common.util.file.FileDownloader;
import com.lirong.module.doc.service.model.DocumentInfo;
import com.lirong.module.doc.service.model.DocumentInfoExtractor;
import com.pdftron.common.PDFNetException;
import com.pdftron.pdf.*;
import io.minio.*;
import org.apache.commons.io.FileUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


public class SyncDocumentFrom210 {

    // Minio配置
    private static final String MINIO_ENDPOINT = "http://*************:9000";
    // 更新Minio凭证 - 请替换为正确的凭证
    private static final String MINIO_ACCESS_KEY = "minioadmin";  // 可能需要更新
    private static final String MINIO_SECRET_KEY = "minioadmin";  // 可能需要更新
    private static final String MINIO_BUCKET = "assets";

    // SFTP配置
    private static final String SFTP_HOST = "*************";
    private static final int SFTP_PORT = 22;
    private static final String SFTP_USER = "root";
    private static final String SFTP_PASSWORD = "wangchao!";
    private static final String LOCAL_TEMP_DIR = "D:\\temp\\downloads";

    // 添加唯一配置名称常量
    private static final String PG_CONFIG_NAME = "postgresql";
    private static final String MYSQL_CONFIG_NAME = "mysql";

    // OpenAI配置
    private static final String OPENAI_API_KEY = "sk-your-api-key";

    private static MinioClient minioClient;
    private static JSch jsch;

    private static final String THUMBNAIL_DIR = "thumbnails";

    private static DocumentInfoExtractor aiExtractor;

    public static void main(String[] args) throws Exception {
        try {
            initializeDependencies();

            // 验证Minio连接
            if (verifyMinioConnection()) {
                System.out.println("Minio连接验证成功，开始处理文档...");
                processDocuments();
            } else {
                System.err.println("Minio连接验证失败，请检查配置和凭证");
            }
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        }
//        File file = downloadViaSftp("/prod-api/original_document/20240424/231219_Monaghan_NATO_CUI.pdf");
//        if(file != null) {
//            System.out.println(file.getAbsoluteFile());
//        }
    }

    private static void initializeDependencies() throws Exception {
        // 初始化文档信息提取器
        aiExtractor = new DocumentInfoExtractor();

        PDFNet.initialize("demo:<EMAIL>:61759e140200000000f86322f6a371657057742c0d563de52f999d61ad");
        // 初始化数据库连接
        initializeDatabase();

        try {
            System.out.println("正在初始化Minio客户端...");
            System.out.println("Minio端点: " + MINIO_ENDPOINT);
            System.out.println("Minio访问密钥: " + MINIO_ACCESS_KEY);
            // 不打印完整的密钥，只显示前几个字符
            System.out.println("Minio密钥: " + MINIO_SECRET_KEY.substring(0, Math.min(3, MINIO_SECRET_KEY.length())) + "...");

            // 初始化Minio客户端
            minioClient = MinioClient.builder()
                    .endpoint(MINIO_ENDPOINT)
                    .credentials(MINIO_ACCESS_KEY, MINIO_SECRET_KEY)
                    .build();
            System.out.println("Minio客户端初始化成功");

            ensureBucketExists();
        } catch (Exception e) {
            System.err.println("初始化Minio客户端失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        // 初始化JSch
        jsch = new JSch();
    }

    private static void initializeDatabase() {
        // PostgreSQL配置
        DruidPlugin dpg = new DruidPlugin("**********************************************", "aikg", "q1w2E#R$t5");
        ActiveRecordPlugin arpg = new ActiveRecordPlugin(PG_CONFIG_NAME, dpg);
        arpg.setDialect(new PostgreSqlDialect());
        arpg.addMapping("doc_documents", Documents.class);
        arpg.addMapping("doc_institutions", Institutions.class);
        dpg.start();
        arpg.start();

        // MySQL配置
        DruidPlugin dpm = new DruidPlugin("***********************************************", "root", "lrkj@2024!A");
        ActiveRecordPlugin arpm = new ActiveRecordPlugin(MYSQL_CONFIG_NAME, dpm);
        arpm.addMapping("original_document", OriginalDocument.class);
        dpm.start();
        arpm.start();
    }

    private static void ensureBucketExists() throws Exception {
        try {
            System.out.println("检查Minio存储桶 '" + MINIO_BUCKET + "' 是否存在...");
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(MINIO_BUCKET).build());

            if (exists) {
                System.out.println("存储桶 '" + MINIO_BUCKET + "' 已存在");
            } else {
                System.out.println("存储桶 '" + MINIO_BUCKET + "' 不存在，尝试创建...");
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(MINIO_BUCKET).build());
                System.out.println("存储桶 '" + MINIO_BUCKET + "' 创建成功");
            }

            // 列出所有存储桶以验证连接
            System.out.println("列出所有存储桶:");
            minioClient.listBuckets().forEach(bucket ->
                System.out.println(" - " + bucket.name() + " (创建于: " + bucket.creationDate() + ")"));

        } catch (Exception e) {
            System.err.println("Minio存储桶操作失败: " + e.getMessage());
            if (e instanceof io.minio.errors.ErrorResponseException) {
                io.minio.errors.ErrorResponseException ere = (io.minio.errors.ErrorResponseException) e;
                System.err.println("错误代码: " + ere.errorResponse().code());
                System.err.println("错误消息: " + ere.errorResponse().message());
            }
            throw e;
        }
    }

    /**
     * 验证Minio连接
     * @return 连接是否成功
     */
    private static boolean verifyMinioConnection() {
        try {
            System.out.println("正在验证Minio连接...");

            // 尝试列出存储桶
            minioClient.listBuckets();
            System.out.println("Minio连接成功！");

            // 尝试检查目标存储桶
            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(MINIO_BUCKET).build());
            if (bucketExists) {
                System.out.println("存储桶 '" + MINIO_BUCKET + "' 存在且可访问");
            } else {
                System.out.println("存储桶 '" + MINIO_BUCKET + "' 不存在，将尝试创建");
            }

            return true;
        } catch (Exception e) {
            System.err.println("Minio连接验证失败: " + e.getMessage());
            if (e instanceof io.minio.errors.ErrorResponseException) {
                io.minio.errors.ErrorResponseException ere = (io.minio.errors.ErrorResponseException) e;
                System.err.println("错误代码: " + ere.errorResponse().code());
                System.err.println("错误消息: " + ere.errorResponse().message());
                System.err.println("请检查Minio凭证是否正确");
            }
            e.printStackTrace();
            return false;
        }
    }

    private static void processDocuments() throws IOException {
        List<ExcelDoc> records = EasyExcel.read("G://Nwt/cache/recv/柴巍/筛选2000年后的.xlsx", ExcelDoc.class, null)
                .autoCloseStream(false)
                .doReadAllSync();

        Map<String, Long> institutionMap = new HashMap<>();

        for (ExcelDoc record : records) {
            try {
                processSingleRecord(record, institutionMap);
            } catch (Exception e) {
                System.err.println("处理记录失败: " + record.getForeignTitle());
                e.printStackTrace();
            }
        }
    }

    private static void processSingleRecord(ExcelDoc record, Map<String, Long> institutionMap) throws Exception {
        Documents existingDoc = new Documents().findFirst(
                "select * from doc_documents where data_source = ? limit 1", record.getDataSource());
        if (existingDoc != null) {
            System.out.println(existingDoc.getStr("foreign_title") + " 已存在");
            return;
        }

        Long institutionId = getInstitutionId(record, institutionMap);
        if (institutionId == null) {
            System.out.println(record.getForeignTitle() + "未找到发布机构：" + record.getPublisher() + " " + record.getWebsite());
            return;
        }

        OriginalDocument originalDoc = new OriginalDocument().findFirst(
                "select * from original_document where id = ?", record.getId());
        if (originalDoc == null) {
            System.out.println("原始文档不存在: " + record.getId());
            return;
        }

        File documentFile = downloadDocument(originalDoc);
        if (documentFile == null) {
            System.out.println("文件下载失败: " + originalDoc.getStr("file_path"));
            return;
        }

        String minioUrl = uploadToMinio(documentFile);

        try (InputStream is = new FileInputStream(documentFile)) {
            DocumentInfo docInfo = processPDF(is, record.getId().toString());

            // 上传缩略图到Minio
            String smallThumbUrl = uploadThumbnailToMinio(docInfo.getSmallThumbnailPath());
            String largeThumbUrl = uploadThumbnailToMinio(docInfo.getLargeThumbnailPath());

            // 保存到数据库
            saveDocumentInfo(record, originalDoc, institutionId, minioUrl, docInfo, smallThumbUrl, largeThumbUrl);
        }

//        String translatedTitle = translateText(originalDoc.getStr("title"));
//        String translatedAbstract = translateText(originalDoc.getStr("abstract"));

//        saveNewDocument(record, originalDoc, institutionId, minioUrl, translatedTitle, translatedAbstract);
    }

    // 缩略图上传Minio
    private static String uploadThumbnailToMinio(String localPath) throws Exception {
        // 定义重试参数
        final int maxRetries = 5; // 最大重试次数
        final long initialBackoffMillis = 2000; // 初始退避时间（2秒）
        final long maxBackoffMillis = 30000; // 最大退避时间（30秒）
        int retryCount = 0;

        File file = new File(localPath);
        String objectName = "thumbnails/" + file.getName();

        System.out.println("正在上传缩略图到Minio: " + localPath);
        System.out.println("目标对象名称: " + objectName);

        String url = String.format("%s/%s/%s", MINIO_ENDPOINT, MINIO_BUCKET, objectName);
        Exception lastException = null;

        while (retryCount <= maxRetries) {
            try {
                minioClient.uploadObject(UploadObjectArgs.builder()
                        .bucket(MINIO_BUCKET)
                        .object(objectName)
                        .filename(localPath)
                        .contentType("image/png")
                        .build());

                System.out.println("缩略图上传成功，URL: " + url);
                return url;
            } catch (Exception e) {
                lastException = e;
                String errorMessage = e.getMessage();

                // 检查是否是MinIO的限流错误
                if (errorMessage != null &&
                    (errorMessage.contains("SlowDownWrite") ||
                     errorMessage.contains("Resource requested is unwritable"))) {

                    retryCount++;
                    if (retryCount > maxRetries) {
                        System.err.println("上传缩略图到Minio失败，MinIO服务器限流，已重试" + maxRetries + "次: " + file.getName());
                        break; // 超过最大重试次数，退出
                    }

                    // 对于限流错误，使用指数退避策略
                    long backoffTime = calculateBackoffTime(retryCount, initialBackoffMillis, maxBackoffMillis);
                    System.out.println("MinIO服务器限流，将在" + backoffTime + "毫秒后进行第" + retryCount + "次重试上传缩略图: " + file.getName());
                    sleep(backoffTime);
                } else {
                    // 其他类型的错误，记录日志并退出
                    System.err.println("上传缩略图到Minio失败: " + errorMessage);
                    if (e instanceof io.minio.errors.ErrorResponseException) {
                        io.minio.errors.ErrorResponseException ere = (io.minio.errors.ErrorResponseException) e;
                        System.err.println("错误代码: " + ere.errorResponse().code());
                        System.err.println("错误消息: " + ere.errorResponse().message());
                    }
                    break;
                }
            }
        }

        // 如果所有重试都失败，抛出最后一个异常
        if (lastException != null) {
            throw lastException;
        } else {
            throw new Exception("上传缩略图到Minio失败，原因未知");
        }
    }

    private static DocumentInfo processPDF(InputStream in, String docId) throws IOException {
        Path thumbnailDir = Paths.get(LOCAL_TEMP_DIR, THUMBNAIL_DIR);
        Files.createDirectories(thumbnailDir);
        DocumentInfo documentInfo;
        try (PDFDoc doc = new PDFDoc(in)) {
            // 提取文本和元数据
            documentInfo = processTextAndMetadata(doc);
            // 生成缩略图
            generateThumbnails(doc, docId, thumbnailDir, documentInfo);
        } catch (PDFNetException e) {
            throw new IOException("PDF处理失败", e);
        }
        return documentInfo;
    }

    private static DocumentInfo processTextAndMetadata(PDFDoc doc) throws PDFNetException, IOException {
        TextExtractor extractor = new TextExtractor();
        StringBuilder fullText = new StringBuilder();

        int pageCount = doc.getPageCount();
        for (int i = 1; i <= pageCount; i++) {
            Page page = doc.getPage(i);
            extractor.begin(page);
            fullText.append(extractor.getAsText());
        }
        // 抽取信息
        DocumentInfo docInfo = aiExtractor.extractInfoFromText(fullText.substring(0, Math.min(fullText.length(), 7500)));

        // 统计信息
        docInfo.setPageCount(pageCount);
        int wordCount = fullText.toString().split("\\s+").length; // 简单的字数统计
        docInfo.setWordCount(wordCount);
        docInfo.setPageCount(pageCount);
        return docInfo;
    }

    /**
     * 生成缩略图
     * @param doc
     * @param docId
     * @param thumbnailDir
     * @param documentInfo
     * @throws PDFNetException
     * @throws IOException
     */
    private static void generateThumbnails(PDFDoc doc, String docId, Path thumbnailDir, DocumentInfo documentInfo) throws PDFNetException, IOException {
        PDFDraw draw = new PDFDraw();
        draw.setDPI(96);

        // 生成大缩略图（首页）
        Page firstPage = doc.getPage(1);
        BufferedImage largeImage = draw.getBitmap(firstPage);
        Path largeThumbPath = thumbnailDir.resolve(docId + "_large.png");
        ImageIO.write(largeImage, "png", largeThumbPath.toFile());

        // 生成小缩略图
        draw.setImageSize(160, 210);
        BufferedImage smallImage = draw.getBitmap(firstPage);
        Path smallThumbPath = thumbnailDir.resolve(docId + "_small.png");
        ImageIO.write(smallImage, "png", smallThumbPath.toFile());

        documentInfo.setLargeThumbnailPath(largeThumbPath.toString());
        documentInfo.setSmallThumbnailPath(smallThumbPath.toString());
    }




    private static Long getInstitutionId(ExcelDoc record, Map<String, Long> institutionMap) throws Exception {
        String publisher = record.getPublisher();
        String website = record.getWebsite();

        // 优先从缓存查询
        if (institutionMap.containsKey(website)) return institutionMap.get(website);
        if (institutionMap.containsKey(publisher)) return institutionMap.get(publisher);

        // 查询数据库
        Institutions institution = new Institutions().findFirst(
                "select * from doc_institutions where official_website like ? limit 1", "%" + website + "%");
        if (institution == null) {
            institution = new Institutions().findFirst(
                    "select * from doc_institutions where chinese_name like ? limit 1", "%" + publisher + "%");
        }

        if (institution != null) {
            Long id = institution.getLong("id");
            institutionMap.put(website, id);
            return id;
        }
        return null;
    }

    private static File downloadDocument(OriginalDocument doc) throws Exception {
        String filePath = doc.getStr("file_path");
        String url = doc.getStr("url");
        if (StringUtils.isBlank(filePath) && StringUtils.isBlank(url)) {
            System.out.println(doc.getStr("title") + " >>> 服务器地址和网络地址均为空");
            return null;
        }
        if (StringUtils.isNotBlank(filePath)) {
            return downloadViaSftp(filePath);
        } else if (url != null) {
            if (url.contains(".pdf")) {
               System.out.println(doc.getStr("title") + " >>> 从网络下载文件：" + url);
               return downloadFromUrl(url);
            } else {
                System.out.println(doc.getStr("title") + " >>> 非pdf文件");
            }
        }
        return null;
    }

    private static File downloadViaSftp(String remotePath) throws JSchException, SftpException, IOException {
        // 检查路径是否有效
        if (remotePath == null || remotePath.trim().isEmpty()) {
            throw new IllegalArgumentException("远程路径不能为空");
        }
        if (remotePath.charAt(0) != '/') {
            throw new IllegalArgumentException("需要绝对路径，当前路径: " + remotePath);
        }

        System.out.println(remotePath);

        Session session = jsch.getSession(SFTP_USER, SFTP_HOST, SFTP_PORT);
        session.setPassword(SFTP_PASSWORD);
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();

        ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
        channel.connect();

        File localDir = new File(LOCAL_TEMP_DIR);
        if (!localDir.exists()) FileUtils.forceMkdir(localDir);

        String fileName = new File(remotePath).getName();
        File localFile = new File(localDir, fileName);
        channel.get(remotePath, localFile.getAbsolutePath());

        channel.disconnect();
        session.disconnect();
        return localFile;
    }

    private static File downloadFromUrl(String url) throws IOException {
        return FileDownloader.downloadFile(url);
    }

    private static String uploadToMinio(File file) throws Exception {
        // 定义重试参数
        final int maxRetries = 5; // 最大重试次数
        final long initialBackoffMillis = 2000; // 初始退避时间（2秒）
        final long maxBackoffMillis = 30000; // 最大退避时间（30秒）
        int retryCount = 0;

        String objectName = "documents/" + file.getName();
        System.out.println("正在上传文档到Minio: " + file.getAbsolutePath());
        System.out.println("目标对象名称: " + objectName);
        System.out.println("文件大小: " + file.length() + " 字节");

        // 方法二：直接构建URL（需要确保存储桶可公开访问）
        String url = String.format("%s/%s/%s", MINIO_ENDPOINT, MINIO_BUCKET, objectName);

        Exception lastException = null;

        while (retryCount <= maxRetries) {
            try {
                // 执行实际上传
                ObjectWriteResponse response = minioClient.uploadObject(
                        UploadObjectArgs.builder()
                                .bucket(MINIO_BUCKET)
                                .object(objectName)
                                .filename(file.getAbsolutePath())
                                .build()
                );

                System.out.println("文档上传成功，URL: " + url);
                System.out.println("ETag: " + response.etag());
                System.out.println("版本ID: " + response.versionId());

                return url;
            } catch (Exception e) {
                lastException = e;
                String errorMessage = e.getMessage();

                // 检查是否是MinIO的限流错误
                if (errorMessage != null &&
                    (errorMessage.contains("SlowDownWrite") ||
                     errorMessage.contains("Resource requested is unwritable"))) {

                    retryCount++;
                    if (retryCount > maxRetries) {
                        System.err.println("上传文档到Minio失败，MinIO服务器限流，已重试" + maxRetries + "次: " + file.getName());
                        break; // 超过最大重试次数，退出
                    }

                    // 对于限流错误，使用指数退避策略
                    long backoffTime = calculateBackoffTime(retryCount, initialBackoffMillis, maxBackoffMillis);
                    System.out.println("MinIO服务器限流，将在" + backoffTime + "毫秒后进行第" + retryCount + "次重试上传文档: " + file.getName());
                    sleep(backoffTime);
                } else {
                    // 其他类型的错误，记录日志并退出
                    System.err.println("上传文档到Minio失败: " + errorMessage);
                    if (e instanceof io.minio.errors.ErrorResponseException) {
                        io.minio.errors.ErrorResponseException ere = (io.minio.errors.ErrorResponseException) e;
                        System.err.println("错误代码: " + ere.errorResponse().code());
                        System.err.println("错误消息: " + ere.errorResponse().message());
                    }
                    break;
                }
            }
        }

        // 如果所有重试都失败，抛出最后一个异常
        if (lastException != null) {
            throw lastException;
        } else {
            throw new Exception("上传文档到Minio失败，原因未知");
        }
    }

    // 定义可能的日期格式
    private static final String[] DATE_FORMATS = {
            "MMMM yyyy",       // November 2023
            "MMMM, yyyy",       // November 2023
            "MMMM dd, yyyy",   // July 17, 2024
            "MMMM d, yyyy",   // November 7, 2014
            "d MMMM yyyy",      // 10 February 2025
            "dd MMMM yyyy",     // 10 February 2025（两位日期）
            "yyyy-MM-dd",      // 2023-11-01
            "yyyy-MM",         // 2023-11
            "yyyy",             // 2023
            "MMM dd, yyyy",     // Jan 21, 2025
            "MMM d, yyyy"       // Nov 6, 2024

    };

    // 自动判断输入格式并转换为合适的输出格式
    public static String convertDate(String inputDate) {
        // 替换多个空格为单个并去除首尾空格
        inputDate = inputDate.replaceAll("\\s+", " ").replaceAll(" 00:00:00", "").trim();
        for (String format : DATE_FORMATS) {
            try {
                DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                        .parseCaseInsensitive() // 忽略大小写
                        .appendPattern(format)
                        .toFormatter(Locale.ENGLISH);
                if (format.equals("MMMM yyyy") || format.equals("MMMM, yyyy") || format.equals("yyyy-MM")) {
                    YearMonth yearMonth = YearMonth.parse(inputDate, inputFormatter);
                    return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                } else if (format.equals("yyyy")) {
                    Year year = Year.parse(inputDate, inputFormatter);
                    return year.format(DateTimeFormatter.ofPattern("yyyy"));
                } else {
                    LocalDate date = LocalDate.parse(inputDate, inputFormatter);
                    return date.format(DateTimeFormatter.ofPattern(getOutputFormat(format)));
                }
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }
        throw new IllegalArgumentException("无法解析日期: " + inputDate);
    }

    // 根据输入格式决定输出格式
    private static String getOutputFormat(String inputFormat) {
        switch (inputFormat) {
            case "MMMM yyyy":
            case "MMMM, yyyy":
            case "yyyy-MM":
                return "yyyy-MM"; // November 2023 -> 2023-11
            case "MMMM dd, yyyy":
            case "MMMM d, yyyy":
            case "MMM dd, yyyy":
            case "MMM d, yyyy":
            case "d MMMM yyyy":   // 新增格式
            case "dd MMMM yyyy":   // 新增格式
                return "yyyy-MM-dd"; // July 17, 2024 -> 2024-07-17
            case "yyyy-MM-dd":
                return "yyyy-MM-dd"; // 2023-11-01 -> 2023-11-01
            case "yyyy":
                return "yyyy"; // 2023 -> 2023
            default:
                throw new IllegalArgumentException("不支持的日期格式: " + inputFormat);
        }
    }

    // 修改后的保存方法
    /**
     * 计算指数退避时间
     *
     * @param retryCount 当前重试次数
     * @param initialBackoffMillis 初始退避时间（毫秒）
     * @param maxBackoffMillis 最大退避时间（毫秒）
     * @return 计算出的退避时间（毫秒）
     */
    private static long calculateBackoffTime(int retryCount, long initialBackoffMillis, long maxBackoffMillis) {
        // 计算指数退避时间：initialBackoffMillis * 2^(retryCount-1)
        long backoffTime = initialBackoffMillis * (long) Math.pow(2, retryCount - 1);
        // 添加一些随机性，避免多个请求同时重试
        backoffTime += (long) (backoffTime * 0.2 * Math.random());
        // 确保不超过最大退避时间
        return Math.min(backoffTime, maxBackoffMillis);
    }

    /**
     * 线程休眠指定时间
     *
     * @param millis 休眠时间（毫秒）
     */
    private static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private static void saveDocumentInfo(ExcelDoc record, OriginalDocument originalDoc,
                                         Long institutionId, String fileUrl,
                                         DocumentInfo docInfo, String smallThumb, String largeThumb) {

        Documents documents = new Documents();
        // 处理发布日期
        if (org.apache.commons.lang3.StringUtils.isNotBlank(record.getPublishDate())) {
            try {
                String[] dates = convertDate(record.getPublishDate()).split("-");
                // 确保至少有年份
                if (dates.length >= 1) {
                    documents.set("publish_year", Integer.parseInt(dates[0]));
                }
                // 如果有月份，则设置月份
                if (dates.length >= 2) {
                    documents.set("publish_month", Integer.parseInt(dates[1]));
                }
                // 如果有日期，则设置日期
                if (dates.length >= 3) {
                    documents.set("publish_day", Integer.parseInt(dates[2]));
                }
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            }

        }
        String titleCn = originalDoc.getStr("title_cn");
        documents
                .set("foreign_title", record.getForeignTitle())
                .set("chinese_title",  StringUtils.isNotBlank(titleCn) ? titleCn : docInfo.getTitleCn())
                .set("foreign_abstract", StringUtils.isNotBlank(originalDoc.getStr("summary")) ? originalDoc.getStr("summary") : docInfo.getSummary())
                .set("chinese_abstract", docInfo.getSummaryCn())
                .set("tags", docInfo.getTags().replaceAll("；", ",").replaceAll(";", ",").replaceAll("、", ",").replaceAll("，", ",").split(","))
                .set("reviewer", docInfo.getCategory()) // 暂存分类名称
                .set("thumbnail_url", smallThumb)
                .set("page_count", docInfo.getPageCount())
                .set("word_count", docInfo.getWordCount())
                .set("language", docInfo.getLanguage())
                .set("original_url", fileUrl)
                .set("data_source", record.getDataSource())
                .set("institution_id", institutionId)
                .set("creator", "sync_doc_task")
                .set("updater", originalDoc.getStr("authors"))  // 暂存作者信息
                .set("create_time", new java.util.Date())
                .set("update_time", new java.util.Date())
                .save();
    }
}