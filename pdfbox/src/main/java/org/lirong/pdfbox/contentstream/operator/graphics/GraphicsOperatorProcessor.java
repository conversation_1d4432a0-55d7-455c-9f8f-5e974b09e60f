/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.lirong.pdfbox.contentstream.operator.graphics;

import org.lirong.pdfbox.contentstream.PDFGraphicsStreamEngine;
import org.lirong.pdfbox.contentstream.operator.OperatorProcessor;
import org.lirong.pdfbox.contentstream.PDFGraphicsStreamEngine;

/**
 * Base class for graphics operators.
 *
 * <AUTHOR>
 */
public abstract class GraphicsOperatorProcessor extends OperatorProcessor
{

    protected GraphicsOperatorProcessor(PDFGraphicsStreamEngine context)
    {
        super(context);
    }

    /**
     * GraphicsOperatorProcessor uses a spezialized engine.
     * 
     * @return PDFGraphicsStreamEngine to be used for processing
     */
    protected PDFGraphicsStreamEngine getGraphicsContext()
    {
        return (PDFGraphicsStreamEngine) getContext();
    }
}
