/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.lirong.pdfbox.contentstream.operator.markedcontent;

import java.io.IOException;
import java.util.List;

import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.lirong.pdfbox.contentstream.PDFStreamEngine;
import org.lirong.pdfbox.contentstream.operator.MissingOperandException;
import org.lirong.pdfbox.contentstream.operator.Operator;
import org.lirong.pdfbox.contentstream.operator.OperatorName;
import org.lirong.pdfbox.contentstream.operator.OperatorProcessor;
import org.lirong.pdfbox.cos.COSBase;
import org.lirong.pdfbox.cos.COSName;
import org.lirong.pdfbox.pdmodel.graphics.PDXObject;
import org.lirong.pdfbox.pdmodel.graphics.form.PDFormXObject;
import org.lirong.pdfbox.pdmodel.graphics.form.PDTransparencyGroup;
import org.lirong.pdfbox.text.PDFMarkedContentExtractor;
import org.lirong.pdfbox.contentstream.PDFStreamEngine;
import org.lirong.pdfbox.contentstream.operator.MissingOperandException;
import org.lirong.pdfbox.contentstream.operator.Operator;
import org.lirong.pdfbox.contentstream.operator.OperatorName;
import org.lirong.pdfbox.contentstream.operator.OperatorProcessor;

/**
 * Do: Draws an XObject.
 *
 * <AUTHOR> Litchfield
 * <AUTHOR> Ivankovits
 */
public class DrawObject extends OperatorProcessor
{
    private static final Logger LOG = LogManager.getLogger(DrawObject.class);

    public DrawObject(PDFStreamEngine context)
    {
        super(context);
    }

    @Override
    public void process(Operator operator, List<COSBase> arguments) throws IOException
    {
        if (arguments.isEmpty())
        {
            throw new MissingOperandException(operator, arguments);
        }
        COSBase base0 = arguments.get(0);
        if (!(base0 instanceof COSName))
        {
            return;
        }
        COSName name = (COSName) base0;
        PDFStreamEngine context = getContext();
        PDXObject xobject = context.getResources().getXObject(name);
        ((PDFMarkedContentExtractor) context).xobject(xobject);

        if (xobject instanceof PDFormXObject)
        {
            try
            {
                context.increaseLevel();
                if (context.getLevel() > 50)
                {
                    LOG.error("recursion is too deep, skipping form XObject");
                    return;
                }
                if (xobject instanceof PDTransparencyGroup)
                {
                    context.showTransparencyGroup((PDTransparencyGroup) xobject);
                }
                else
                {
                    context.showForm((PDFormXObject) xobject);
                }
            }
            finally
            {
                context.decreaseLevel();
            }
        }
    }

    @Override
    public String getName()
    {
        return OperatorName.DRAW_OBJECT;
    }
}
