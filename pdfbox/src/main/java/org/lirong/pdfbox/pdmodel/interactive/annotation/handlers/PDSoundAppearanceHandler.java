/*
 * Copyright 2018 The Apache Software Foundation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.lirong.pdfbox.pdmodel.interactive.annotation.handlers;

import org.lirong.pdfbox.pdmodel.PDDocument;
import org.lirong.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.lirong.pdfbox.pdmodel.PDDocument;
import org.lirong.pdfbox.pdmodel.interactive.annotation.PDAnnotation;

public class PDSoundAppearanceHandler extends PDAbstractAppearanceHandler
{
    public PDSoundAppearanceHandler(PDAnnotation annotation)
    {
        super(annotation);
    }

    public PDSoundAppearanceHandler(PDAnnotation annotation, PDDocument document)
    {
        super(annotation, document);
    }

    @Override
    public void generateNormalAppearance()
    {
        // TODO to be implemented
    }

    @Override
    public void generateRolloverAppearance()
    {
        // TODO to be implemented
    }

    @Override
    public void generateDownAppearance()
    {
        // TODO to be implemented
    }
}
