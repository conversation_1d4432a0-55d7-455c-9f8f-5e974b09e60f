package com.lirong.module.portal.controller.admin.vo;

import com.lirong.module.intelligence.api.source.dto.SourceDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import lombok.Data;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

@Data
public class NewsDO {

    private String id;
    private String title;
    private String rawTitle;
    private String subtitle;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date publishDate;
    private String publishTime;
    private String author;
    private String summary;
    private String rawSummary;
    private String thumbnail;
    private String thumbnailDescription;
    /**
     * 翻译后内容（不含格式）
     */
    private String content;
    /**
     * 原始内容（不含格式）
     */
    private String rawContent;
    /**
     * 翻译后网页内容（含格式）
     */
    private String text;
    /**
     * 原始网页内容（含格式）
     */
    private String rawText;

    @JsonSerialize(using = TagsSerializer.class)
    private String tags;
    private String sourceUrl;
    private String sourceName;
    private Date createTime;

    private String category;
    private JSONArray keywords;
    private JSONObject entities;
    private SourceDTO source;

    // 自定义序列化器
    public static class TagsSerializer extends StdSerializer<String> {
        public TagsSerializer() {
            this(null);
        }

        public TagsSerializer(Class<String> t) {
            super(t);
        }

        @Override
        public void serialize(String value, JsonGenerator gen, SerializerProvider arg2) throws IOException {
            if (value == null) {
                gen.writeNull();
            } else {
                gen.writeObject(Arrays.stream(value.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()));
            }
        }
    }

}
