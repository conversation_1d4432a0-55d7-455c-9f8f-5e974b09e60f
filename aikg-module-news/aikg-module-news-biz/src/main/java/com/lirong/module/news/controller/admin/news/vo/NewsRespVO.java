package com.lirong.module.news.controller.admin.news.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 新闻 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NewsRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private String id;

    @Schema(description = "文章类型")
    @ExcelProperty("文章类型")
    private String articleType;

    @Schema(description = "分类")
    @ExcelProperty("分类")
    private String category;

    @Schema(description = "内容")
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private String createTime;

    @Schema(description = "倾向性")
    @ExcelProperty("倾向性")
    private String emotional;

    @Schema(description = "实体（国家、机构、人员）")
    @ExcelProperty("实体（国家、机构、人员）")
    private String entities;

    @Schema(description = "关键词")
    @ExcelProperty("关键词")
    private String keywords;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private String publishDate;

    @Schema(description = "作者")
    @ExcelProperty("作者")
    private String authors;

    @Schema(description = "发布机构")
    @ExcelProperty("发布机构")
    private String publisher;

    @Schema(description = "原始内容")
    @ExcelProperty("原始内容")
    private String rawContent;

    @Schema(description = "原始摘要")
    @ExcelProperty("原始摘要")
    private String rawSummary;

    @Schema(description = "原始内容带HTML格式")
    @ExcelProperty("原始内容带HTML格式")
    private String rawText;

    @Schema(description = "原始标题")
    @ExcelProperty("原始标题")
    private String rawTitle;

    @Schema(description = "来源名称")
    @ExcelProperty("来源名称")
    private String sourceName;

    @Schema(description = "数据来源")
    @ExcelProperty("数据来源")
    private String sourceUrl;

    @Schema(description = "副标题")
    @ExcelProperty("副标题")
    private String subtitle;

    @Schema(description = "摘要")
    @ExcelProperty("摘要")
    private String summary;

    @Schema(description = "标签")
    @ExcelProperty("标签")
    private String tags;

    @Schema(description = "内容带HTML格式")
    @ExcelProperty("内容带HTML格式")
    private String text;

    @Schema(description = "缩略图")
    @ExcelProperty("缩略图")
    private String thumbnail;

    @Schema(description = "缩略图描述")
    @ExcelProperty("缩略图描述")
    private String thumbnailDescription;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "域名")
    @ExcelProperty("域名")
    private String domain;

}