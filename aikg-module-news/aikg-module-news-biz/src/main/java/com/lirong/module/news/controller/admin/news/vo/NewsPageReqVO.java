package com.lirong.module.news.controller.admin.news.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lirong.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.lirong.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 新闻分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NewsPageReqVO extends PageParam {

    @Schema(description = "文章类型")
    private String articleType;

    @Schema(description = "分类")
    private String category;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    @Schema(description = "倾向性")
    private String emotional;

    @Schema(description = "实体（国家、机构、人员）")
    private String entities;

    @Schema(description = "关键词")
    private String keywords;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date publishDate;

    @Schema(description = "作者")
    private String authors;

    @Schema(description = "发布机构")
    private String publisher;

    @Schema(description = "原始内容")
    private String rawContent;

    @Schema(description = "原始摘要")
    private String rawSummary;

    @Schema(description = "原始内容带HTML格式")
    private String rawText;

    @Schema(description = "原始标题")
    private String rawTitle;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "数据来源")
    private String sourceUrl;

    @Schema(description = "副标题")
    private String subtitle;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "内容带HTML格式")
    private String text;

    @Schema(description = "缩略图")
    private String thumbnail;

    @Schema(description = "缩略图描述")
    private String thumbnailDescription;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "域名")
    private String domain;

    @Schema(description = "检索属性")
    private String searchProperty;

    @Schema(description = "开始年")
    private String startYear;

    @Schema(description = "结束年")
    private String endYear;

    @Schema(description = "排序属性")
    private String sortProperty;

}