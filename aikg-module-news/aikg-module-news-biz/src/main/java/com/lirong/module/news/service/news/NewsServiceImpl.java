package com.lirong.module.news.service.news;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lirong.framework.common.exception.ServiceException;
import com.lirong.module.infra.api.file.FileApi;
import com.lirong.module.pipeline.api.es.ElasticsearchApi;
import com.lirong.module.pipeline.api.es.dto.Entity;
import com.lirong.module.pipeline.api.es.dto.Keyword;
import com.lirong.module.pipeline.api.es.dto.NewsMessage;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import com.lirong.module.news.controller.admin.news.vo.*;
import com.lirong.module.news.dal.dataobject.news.NewsDO;
import com.lirong.framework.common.pojo.PageResult;

import com.lirong.module.news.dal.mysql.news.NewsMapper;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 新闻 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NewsServiceImpl implements NewsService {

    @Resource
    private NewsMapper newsMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private ElasticsearchApi elasticsearchApi;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private static final String es_index = "ai_news";

    @Override
    public void deleteNews(String id) {
        try {
            DeleteRequest request = new DeleteRequest(es_index, id);
            DeleteResponse deleteResponse = restHighLevelClient.delete(request, RequestOptions.DEFAULT);
            RestStatus status = deleteResponse.status();
            System.out.println(status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public NewsDO getNews(String id) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        GetRequest getRequest = new GetRequest(es_index, id);
        try {
            GetResponse getResponse = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
            if (getResponse.isExists()) {
                String sourceAsString = getResponse.getSourceAsString();
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> map = mapper.readValue(sourceAsString, Map.class);
                NewsDO newsDO = new NewsDO();
                newsDO.setId(id);
                newsDO.setTitle(getMapString(map.get("title")));
                newsDO.setArticleType(getMapString(map.get("articleType")));
                newsDO.setCategory(getMapString(map.get("category")));
                newsDO.setPublisher(getMapString(map.get("publisher")));
                String publishDate = getMapString(map.get("publishDate"));
                if (!publishDate.isEmpty()) {
                    Date date = new Date(Long.parseLong(publishDate));
                    String formattedDate = sdf.format(date);
                    newsDO.setPublishDate(formattedDate);
                }
                newsDO.setEmotional(getMapString(map.get("emotional")));
                newsDO.setSourceName(getMapString(map.get("sourceName")));
                newsDO.setContent(getMapString(map.get("content")));
                newsDO.setEntities(getMapString(map.get("entities")));
                if (map.get("keywords") != null) {
                    String keyword = JSONObject.toJSONString(map.get("keywords"));
                    List<Keyword> k = JSONObject.parseArray(keyword, Keyword.class);
                    String collect = k.stream().map(Keyword::getCn).collect(Collectors.joining("，"));
                    newsDO.setKeywords(collect);
                }
                String createTime = getMapString(map.get("createTime"));
                if (!createTime.isEmpty()) {
                    Date date = new Date(Long.parseLong(createTime));
                    String formattedDate = sdf.format(date);
                    newsDO.setCreateTime(formattedDate);
                }
                newsDO.setPublisher(getMapString(map.get("publisher")));
                newsDO.setRawContent(getMapString(map.get("rawContent")));
                newsDO.setRawSummary(getMapString(map.get("rawSummary")));
                newsDO.setRawText(getMapString(map.get("rawText")));
                newsDO.setRawTitle(getMapString(map.get("rawTitle")));
                newsDO.setSourceUrl(getMapString(map.get("sourceUrl")));
                newsDO.setSubtitle(getMapString(map.get("subtitle")));
                newsDO.setSummary(getMapString(map.get("summary")));
                newsDO.setTags(getMapString(map.get("tags")));
                newsDO.setText(getMapString(map.get("text")));
                newsDO.setThumbnail(getMapString(map.get("thumbnail")));
                newsDO.setThumbnailDescription(getMapString(map.get("thumbnailDescription")));
                newsDO.setDomain(getMapString(map.get("domain")));
                return newsDO;
            } else {
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private String fileConfigIp;
    private String newFileConfigIp;
    private Pattern pattern;

    /**
     * 上传新闻
     *
     * @param file 压缩数据包
     * @return 结果
     */
    @Override
    public int upload(MultipartFile file) {
        int uploadCount = 0;
        newFileConfigIp = fileApi.getFileConfigIp();
        fileConfigIp = "";
        String urlPattern = "\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b";
        pattern = Pattern.compile(urlPattern);
        try {
            // 获取 ZIP 文件的输入流
            InputStream inputStream = file.getInputStream();
            ZipInputStream zipInputStream = new ZipInputStream(inputStream);

            // 遍历 ZIP 文件中的每个条目
            ZipEntry zipEntry;
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                String zipEntryName = zipEntry.getName();
                if (zipEntryName.toLowerCase().endsWith(".json")) {
                    String jsonString = "";
                    try {
                        InputStreamReader isr = new InputStreamReader(zipInputStream);
                        BufferedReader br = new BufferedReader(isr);
                        String line;
                        while ((line = br.readLine()) != null) {
                            jsonString += line;
                        }
                        isr.close();
                        br.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    if (!jsonString.isEmpty()) {
                        int count = uploadData(jsonString);
                        uploadCount += count;
                    }
                } else {
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                        byteArrayOutputStream.write(buffer, 0, bytesRead);
                    }
                    byte[] fileBytes = byteArrayOutputStream.toByteArray();
                    String filePath = fileApi.createFile(zipEntryName, fileBytes);
                    System.out.println("文件【" + filePath + "】上传成功！");
                    byteArrayOutputStream.close();
                }
                // 处理完当前条目后，关闭它
                zipInputStream.closeEntry();
            }
            inputStream.close();
            zipInputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return uploadCount;
    }

    /**
     * 获得新闻发布机构
     *
     * @return 新闻发布机构
     */
    @Override
    public List<Map<String, Object>> getPublisher() {
        try {
            SearchRequest searchRequest = new SearchRequest(es_index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            TermsAggregationBuilder aggregation = AggregationBuilders
                    .terms("unique_publishers")
                    .field("publisher")
                    .size(1000)
                    .order(BucketOrder.count(false));
            searchSourceBuilder.aggregation(aggregation);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            Terms terms = searchResponse.getAggregations().get("unique_publishers");
            List<Map<String, Object>> uniquePublishersWithCounts = new ArrayList<>();
            for (Terms.Bucket entry : terms.getBuckets()) {
                Map<String, Object> publisherInfo = new HashMap<>();
                publisherInfo.put("publisher", entry.getKeyAsString());
                publisherInfo.put("count", entry.getDocCount());
                uniquePublishersWithCounts.add(publisherInfo);
            }
            return uniquePublishersWithCounts;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * 统计索引中文档总数
     * @param indexName
     * @return
     * @throws IOException
     */
    private long countDocuments(String indexName) {
        try {
            Request request = new Request("GET", "/" + indexName + "/_count");
            Response response = restHighLevelClient.getLowLevelClient().performRequest(request);

            // 解析响应体获取文档总数
            String responseBody = new String(response.getEntity().getContent().readAllBytes());
            return JSON.parseObject(responseBody).getLongValue("count");
        } catch (IOException e) {
            return 0;
        }
    }

    @Override
    public long countDocuments() {
        return countDocuments(es_index);
    }

    public int uploadData(String jsonString) {
        int uploadCount = 0;
        List<NewsMessage> newsMessageList = JSONArray.parseArray(jsonString, NewsMessage.class);
        if (fileConfigIp.isEmpty()) {
            newsMessageListFor:
            for (NewsMessage newsMessage : newsMessageList) {
                String urlString = "";
                String thumbnail = newsMessage.getThumbnail();
                if (thumbnail != null && !thumbnail.isEmpty()) {
                    urlString = thumbnail;
                } else {
                    urlString = newsMessage.getRawText();
                }
                Matcher matcher = pattern.matcher(urlString);
                while (matcher.find()) {
                    String ip = matcher.group();
                    if (ip != null && !ip.isEmpty()) {
                        fileConfigIp = ip;
                        break newsMessageListFor;
                    }
                }
            }
        }
        for (NewsMessage newsMessage : newsMessageList) {
            //替换文件IP
            String thumbnail = newsMessage.getThumbnail();
            if (thumbnail != null && !thumbnail.isEmpty() && !fileConfigIp.isEmpty() && !newFileConfigIp.isEmpty()) {
                newsMessage.setThumbnail(thumbnail.replaceAll(fileConfigIp, newFileConfigIp));
            }
            String text = newsMessage.getText();
            if (text != null && !text.isEmpty() && !fileConfigIp.isEmpty() && !newFileConfigIp.isEmpty()) {
                newsMessage.setText(text.replaceAll(fileConfigIp, newFileConfigIp));
            }
            String rawText = newsMessage.getRawText();
            if (rawText != null && !rawText.isEmpty() && !fileConfigIp.isEmpty() && !newFileConfigIp.isEmpty()) {
                newsMessage.setRawText(rawText.replaceAll(fileConfigIp, newFileConfigIp));
            }
            String id = newsMessage.getId();
            //newsMessage.setId("");
            try {
                SearchRequest searchRequest = new SearchRequest(es_index);
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                searchSourceBuilder.query(QueryBuilders.matchPhraseQuery("sourceUrl", newsMessage.getSourceUrl()));
                searchRequest.source(searchSourceBuilder);
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                if (searchHits.getTotalHits().value <= 0) {
                    boolean status = elasticsearchApi.saveNews(newsMessage);
                    if (!status) {
                        System.err.println("ID为：" + id + "的新闻导入失败！");
                    }
                    uploadCount++;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return uploadCount;
    }

    @Override
    public PageResult<NewsDO> getNewsPage(NewsPageReqVO pageReqVO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 分页
        Pageable pageable = PageRequest.of(pageReqVO.getPageNo() - 1, pageReqVO.getPageSize());
        List<NewsDO> newsDOList = new ArrayList<>();
        // 构建查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //模糊匹配
        String keywords = pageReqVO.getKeywords();
        if (keywords == null || keywords.isEmpty()) {
            boolQueryBuilder.must(QueryBuilders.matchAllQuery());
        } else {
            BoolQueryBuilder keywordQueryBuilder = QueryBuilders.boolQuery();
            // 模糊匹配
            String searchProperty = pageReqVO.getSearchProperty();
            if (searchProperty == null || searchProperty.isEmpty() || searchProperty.equals("title")) {
                keywordQueryBuilder.should(QueryBuilders.matchPhraseQuery("title", keywords));
                keywordQueryBuilder.should(QueryBuilders.matchPhraseQuery("rawTitle", keywords));
            }
            if (searchProperty == null || searchProperty.isEmpty() || searchProperty.equals("text")) {
                keywordQueryBuilder.should(QueryBuilders.matchPhraseQuery("text", keywords));
                keywordQueryBuilder.should(QueryBuilders.matchPhraseQuery("rawText", keywords));
            }
            boolQueryBuilder.must(keywordQueryBuilder);
        }
        String publisher = pageReqVO.getPublisher();
        if (publisher != null && !publisher.isEmpty()) {
            boolQueryBuilder.must(QueryBuilders.termQuery("publisher", publisher));
        }
        String startDate = "";
        String startYear = pageReqVO.getStartYear();
        if (startYear != null && !startYear.isEmpty()) {
            startDate = startYear + "-01-01";
        }
        String endYear = pageReqVO.getEndYear();
        String endDate = "";
        if (endYear != null && !endYear.isEmpty()) {
            int endYearInt = Integer.parseInt(endYear);
            if (endYearInt == LocalDate.now().getYear()) {
                endDate = LocalDate.now().toString();
            } else {
                endDate = Year.of(endYearInt).atDay(Year.of(endYearInt).length()).toString();
            }
        }
        if (!startDate.isEmpty() || !endDate.isEmpty()) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("publishDate");
            if (!startDate.isEmpty()) {
                rangeQueryBuilder.gte(startDate);
            }
            if (!endDate.isEmpty()) {
                rangeQueryBuilder.lte(endDate);
            }
            boolQueryBuilder.must(rangeQueryBuilder);
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder().query(boolQueryBuilder).from(pageable.getPageNumber() * pageable.getPageSize()).size(pageable.getPageSize());
        String sortProperty = pageReqVO.getSortProperty();
        if (sortProperty == null || sortProperty.isEmpty() || sortProperty.equals("publishDate")) {
            FieldSortBuilder sortBuilder = SortBuilders.fieldSort("publishDate").order(SortOrder.DESC);
            searchSourceBuilder.sort(sortBuilder);
        }

        SearchRequest searchRequest = new SearchRequest(es_index);
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException(10002, String.format("操作错误，请联系管理员！%s", e.getMessage()));
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        // 遍历返回的内容进行处理
        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            NewsDO newsDO = new NewsDO();
            newsDO.setId(hit.getId());
            newsDO.setTitle(getMapString(sourceAsMap.get("title")));
            newsDO.setArticleType(getMapString(sourceAsMap.get("articleType")));
            newsDO.setCategory(getMapString(sourceAsMap.get("category")));
            newsDO.setPublisher(getMapString(sourceAsMap.get("publisher")));
            String publishDate = getMapString(sourceAsMap.get("publishDate"));
            if (!publishDate.isEmpty()) {
                Date date = new Date(Long.parseLong(publishDate));
                String formattedDate = sdf.format(date);
                newsDO.setPublishDate(formattedDate);
            }
            newsDO.setEmotional(getMapString(sourceAsMap.get("emotional")));
            newsDO.setSourceName(getMapString(sourceAsMap.get("sourceName")));
            newsDO.setContent(getMapString(sourceAsMap.get("content")));

            String createTime = getMapString(sourceAsMap.get("createTime"));
            if (!createTime.isEmpty()) {
                Date date = new Date(Long.parseLong(createTime));
                String formattedDate = sdf.format(date);
                newsDO.setCreateTime(formattedDate);
            }

            newsDO.setPublisher(getMapString(sourceAsMap.get("publisher")));
            newsDO.setRawContent(getMapString(sourceAsMap.get("rawContent")));
            newsDO.setRawSummary(getMapString(sourceAsMap.get("rawSummary")));
            newsDO.setRawText(getMapString(sourceAsMap.get("rawText")));
            newsDO.setRawTitle(getMapString(sourceAsMap.get("rawTitle")));
            newsDO.setSourceUrl(getMapString(sourceAsMap.get("sourceUrl")));
            newsDO.setSubtitle(getMapString(sourceAsMap.get("subtitle")));
            newsDO.setSummary(getMapString(sourceAsMap.get("summary")));
            newsDO.setText(getMapString(sourceAsMap.get("text")));
            newsDO.setThumbnail("http://*************:9000/assets/thumbnail-Workers_of_Apis_mellifera_Reared_in_Small-Cell_Com.png");
            //newsDO.setThumbnail(getMapString(sourceAsMap.get("thumbnail")));
            newsDO.setThumbnailDescription(getMapString(sourceAsMap.get("thumbnailDescription")));
            newsDO.setDomain(getMapString(sourceAsMap.get("domain")));
            newsDOList.add(newsDO);
        }
        PageResult<NewsDO> pageResult = new PageResult<NewsDO>();
        long total = searchResponse.getHits().getTotalHits().value;
        pageResult.setList(newsDOList);
        pageResult.setTotal(total);
        //esDataTo(hits);
        return pageResult;
    }

    public void esDataTo(SearchHit[] hits) {
        List<NewsMessage> newsMessageList = new ArrayList<>();
        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            NewsMessage newsMessage = new NewsMessage();
            newsMessage.setTitle(getMapString(sourceAsMap.get("title")));
            newsMessage.setArticleType(getMapString(sourceAsMap.get("articleType")));
            newsMessage.setCategory(getMapString(sourceAsMap.get("category")));
            newsMessage.setPublisher(getMapString(sourceAsMap.get("publisher")));
            String publishDate = getMapString(sourceAsMap.get("publishDate"));
            if (!publishDate.isEmpty()) {
                Date date = new Date(Long.parseLong(publishDate));
                newsMessage.setPublishDate(date);
            }
            newsMessage.setEmotional(getMapString(sourceAsMap.get("emotional")));
            newsMessage.setSourceName(getMapString(sourceAsMap.get("sourceName")));
            newsMessage.setContent(getMapString(sourceAsMap.get("content")));

            if (sourceAsMap.get("entities") != null) {
                Map<String, Object> entities = (Map<String, Object>) sourceAsMap.get("entities");
                Entity e = JSONObject.parseObject(JSONObject.toJSONString(entities), Entity.class);
                newsMessage.setEntities(e);
            }

            if (sourceAsMap.get("keywords") != null) {
                String keyword = JSONObject.toJSONString(sourceAsMap.get("keywords"));
                List<Keyword> k = JSONObject.parseObject(keyword, List.class);
                newsMessage.setKeywords(k);
            }
            String createTime = getMapString(sourceAsMap.get("createTime"));
            if (!createTime.isEmpty()) {
                Date date = new Date(Long.parseLong(createTime));
                newsMessage.setCreateTime(date);
            }

            if (sourceAsMap.get("authors") != null) {
                List<String> authors = (List<String>) sourceAsMap.get("authors");
                newsMessage.setAuthors(authors);
            }
            newsMessage.setPublisher(getMapString(sourceAsMap.get("publisher")));
            newsMessage.setRawContent(getMapString(sourceAsMap.get("rawContent")));
            newsMessage.setRawSummary(getMapString(sourceAsMap.get("rawSummary")));
            newsMessage.setRawText(getMapString(sourceAsMap.get("rawText")));
            newsMessage.setRawTitle(getMapString(sourceAsMap.get("rawTitle")));
            newsMessage.setSourceUrl(getMapString(sourceAsMap.get("sourceUrl")));
            newsMessage.setSubtitle(getMapString(sourceAsMap.get("subtitle")));
            newsMessage.setSummary(getMapString(sourceAsMap.get("summary")));

            if (sourceAsMap.get("tags") != null) {
                List<String> t = (List<String>) sourceAsMap.get("tags");
                newsMessage.setTags(t);
            }
            newsMessage.setText(getMapString(sourceAsMap.get("text")));
            newsMessage.setThumbnail(getMapString(sourceAsMap.get("thumbnail")));
            newsMessage.setThumbnailDescription(getMapString(sourceAsMap.get("thumbnailDescription")));
            newsMessage.setDomain(getMapString(sourceAsMap.get("domain")));
            newsMessageList.add(newsMessage);
        }
        convertToJsonAndSave(JSONObject.toJSONString(newsMessageList), "E:\\Download\\Edge\\json.json");
    }

    public void convertToJsonAndSave(String data, String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(data);
            System.out.println("JSON has been written to " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String getMapString(Object object) {
        if (object != null && !String.valueOf(object).isEmpty()) {
            return String.valueOf(object);
        }
        return "";
    }
}