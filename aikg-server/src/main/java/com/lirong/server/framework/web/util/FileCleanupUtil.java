package com.lirong.server.framework.web.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.stream.Stream;

/**
 * 文件清理工具类，定期清理临时文件
 */
@Component
@Slf4j
public class FileCleanupUtil {

    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupTempFiles() {
        log.info("开始执行临时文件清理任务");
        
        // 获取配置的临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        String customTempDir = tempDir + "/aikg-uploads";
        
        // 清理自定义临时目录
        cleanDirectory(Paths.get(customTempDir), Duration.ofDays(1));
        
        // 清理Tomcat临时目录中的上传文件
        cleanTomcatTempDir(tempDir);
        
        log.info("临时文件清理任务完成");
    }
    
    /**
     * 清理指定目录中超过指定时间的文件
     * 
     * @param directory 目录路径
     * @param maxAge 文件最大保留时间
     */
    private void cleanDirectory(Path directory, Duration maxAge) {
        if (!Files.exists(directory)) {
            return;
        }
        
        Instant now = Instant.now();
        Instant cutoff = now.minus(maxAge);
        
        try (Stream<Path> paths = Files.list(directory)) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> {
                     try {
                         return Files.getLastModifiedTime(path).toInstant().isBefore(cutoff);
                     } catch (IOException e) {
                         return false;
                     }
                 })
                 .forEach(path -> {
                     try {
                         Files.delete(path);
                         log.debug("已删除过期临时文件: {}", path);
                     } catch (IOException e) {
                         log.warn("删除临时文件失败: {}", path, e);
                     }
                 });
        } catch (IOException e) {
            log.error("清理目录失败: {}", directory, e);
        }
    }
    
    /**
     * 清理Tomcat临时目录中的上传文件
     * 
     * @param tempDir 系统临时目录
     */
    private void cleanTomcatTempDir(String tempDir) {
        File dir = new File(tempDir);
        File[] tomcatDirs = dir.listFiles((d, name) -> name.startsWith("tomcat."));
        
        if (tomcatDirs == null || tomcatDirs.length == 0) {
            return;
        }
        
        for (File tomcatDir : tomcatDirs) {
            if (tomcatDir.isDirectory()) {
                // 递归查找work目录
                findAndCleanWorkDir(tomcatDir);
            }
        }
    }
    
    /**
     * 查找并清理Tomcat的work目录
     * 
     * @param dir 目录
     */
    private void findAndCleanWorkDir(File dir) {
        if ("work".equals(dir.getName())) {
            // 找到work目录，清理上传文件
            cleanUploadFiles(dir);
            return;
        }
        
        File[] subDirs = dir.listFiles(File::isDirectory);
        if (subDirs != null) {
            for (File subDir : subDirs) {
                findAndCleanWorkDir(subDir);
            }
        }
    }
    
    /**
     * 清理上传文件
     * 
     * @param workDir work目录
     */
    private void cleanUploadFiles(File workDir) {
        // 查找所有上传文件
        File[] files = workDir.listFiles((d, name) -> 
                name.startsWith("upload_") && name.endsWith(".tmp"));
        
        if (files != null) {
            Instant cutoff = Instant.now().minus(Duration.ofDays(1));
            
            for (File file : files) {
                Instant lastModified = Instant.ofEpochMilli(file.lastModified());
                if (lastModified.isBefore(cutoff)) {
                    boolean deleted = file.delete();
                    if (deleted) {
                        log.debug("已删除过期Tomcat上传临时文件: {}", file.getAbsolutePath());
                    } else {
                        log.debug("无法删除Tomcat上传临时文件: {}", file.getAbsolutePath());
                    }
                }
            }
        }
    }
    
    /**
     * 手动清理MultipartFile对应的临时文件
     * 可在处理完文件后调用此方法
     * 
     * @param file MultipartFile对象
     */
    public static void cleanupMultipartFile(MultipartFile file) {
        if (file == null) {
            return;
        }
        
        try {
            // 尝试通过反射获取原始文件对象并删除
            // 注意：这是一种尝试性的方法，可能不适用于所有情况
            java.lang.reflect.Field field = file.getClass().getDeclaredField("file");
            field.setAccessible(true);
            Object tempFile = field.get(file);
            
            if (tempFile instanceof File) {
                File f = (File) tempFile;
                if (f.exists() && !f.delete()) {
                    f.deleteOnExit(); // 如果无法立即删除，则在JVM退出时删除
                }
            }
        } catch (Exception e) {
            // 忽略异常，这只是一种尝试性的清理方法
            log.debug("尝试清理MultipartFile临时文件失败", e);
        }
    }
}
