package com.lirong.server.framework.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件上传配置
 */
@Configuration
@Slf4j
public class MultipartConfig {

    /**
     * 自定义 MultipartResolver，用于处理文件上传
     */
    @Bean
    public MultipartResolver multipartResolver() {
        return new StandardServletMultipartResolver();
    }

    /**
     * 应用启动时确保临时目录存在并有正确的权限
     */
    @Bean
    public CommandLineRunner initMultipartTempDir(Environment environment) {
        return args -> {
            // 获取配置的临时目录
            String location = environment.getProperty("spring.servlet.multipart.location");
            if (location == null) {
                // 如果没有配置，使用系统临时目录
                location = System.getProperty("java.io.tmpdir") + "/aikg-uploads";
            }
            
            log.info("初始化文件上传临时目录: {}", location);
            
            // 创建目录
            Path tempDir = Paths.get(location);
            if (!Files.exists(tempDir)) {
                try {
                    Files.createDirectories(tempDir);
                    log.info("创建文件上传临时目录成功: {}", tempDir);
                } catch (Exception e) {
                    log.error("创建文件上传临时目录失败: {}", tempDir, e);
                }
            }
            
            // 设置目录权限
            File dir = tempDir.toFile();
            if (!dir.canWrite() || !dir.canRead()) {
                boolean success = dir.setWritable(true, false) && 
                                 dir.setReadable(true, false);
                if (success) {
                    log.info("设置文件上传临时目录权限成功");
                } else {
                    log.warn("设置文件上传临时目录权限失败，可能会导致文件上传问题");
                }
            }
            
            // 添加JVM关闭钩子，清理临时文件
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("应用关闭，清理文件上传临时目录: {}", tempDir);
                try {
                    // 仅删除文件，保留目录结构
                    Files.list(tempDir).forEach(file -> {
                        try {
                            Files.deleteIfExists(file);
                        } catch (Exception e) {
                            // 忽略删除失败的文件
                            log.debug("删除临时文件失败: {}", file, e);
                        }
                    });
                } catch (Exception e) {
                    log.warn("清理文件上传临时目录失败", e);
                }
            }));
        };
    }
}
