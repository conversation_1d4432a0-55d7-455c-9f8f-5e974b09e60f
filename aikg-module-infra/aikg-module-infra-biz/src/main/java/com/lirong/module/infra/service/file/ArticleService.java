package com.lirong.module.infra.service.file;

import com.lirong.module.infra.controller.admin.file.vo.article.ArticleDTO;
import com.lirong.module.infra.dal.dataobject.file.ArticleDO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface ArticleService {

    /**
     * 加载文件
     */
    boolean loadFile();

    List<ArticleDO> searchAllData(ArticleDTO articleDTO);

    IPage<ArticleDO> queryHighLightWordOther(ArticleDTO articleDTO, HttpServletRequest request) ;
}
