package com.lirong.module.infra.controller.admin.file;


import com.lirong.framework.apilog.core.annotation.ApiAccessLog;
import com.lirong.framework.common.pojo.CommonResult;
import com.lirong.framework.common.util.object.BeanUtils;
import com.lirong.framework.excel.core.util.ExcelUtils;
import com.lirong.module.infra.controller.admin.file.vo.article.ArticleDTO;
import com.lirong.module.infra.dal.dataobject.file.ArticleDO;
import com.lirong.module.infra.service.file.ArticleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

import static com.lirong.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.lirong.framework.common.pojo.CommonResult.success;

@RestController
@RequestMapping("/infra/article")
@Validated
@Slf4j
public class ArticleController {

    @Autowired
    private ArticleService articleService;

    @PostMapping("/init")
    public CommonResult<String> init() throws Exception {
        boolean result = articleService.loadFile();
        return success("加载完毕");
    }

    /**
     * 告警信息高亮分词分页查询
     *
     * @param articleDTO
     * @return
     */

    @PostMapping("search")
    public CommonResult queryHighLightWordDoc(@RequestBody ArticleDTO articleDTO, HttpServletRequest request) {
        IPage<ArticleDO> warningInfoListPage = articleService.queryHighLightWordOther(articleDTO,request);
        return success(warningInfoListPage);
    }

    /**
     * 导出Excel
     * @param articleDTO
     * @param response
     * @throws IOException
     */
    @GetMapping("/export-excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportData(ArticleDTO articleDTO, HttpServletResponse response) throws IOException {
        // 查询数据
        List<ArticleDO> list = articleService.searchAllData(articleDTO);
        // 导出 Excel
        ExcelUtils.write(response, "检索结果.xlsx", "数据", ArticleDO.class, BeanUtils.toBean(list, ArticleDO.class));
    }

}
