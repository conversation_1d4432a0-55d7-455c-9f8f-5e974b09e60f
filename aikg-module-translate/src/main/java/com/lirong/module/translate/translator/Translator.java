package com.lirong.module.translate.translator;

//import com.lirong.module.translate.pdfbox.Paragraph;
import com.lirong.module.translate.utils.TranslateUtil;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.service.AiServices;

import java.util.List;

public abstract class Translator {

    protected StringBuilder glossary;
    protected ChatLanguageModel model;

    protected abstract void initModel(List<Glossary> glossaries);

    public String translate(String content, String sourceLanguage, String targetLanguage) {
        AiServices<TranslateUtil> aiServices = AiServices.builder(TranslateUtil.class)
                .chatMemoryProvider(memoryId -> MessageWindowChatMemory.withMaxMessages(30))
                .chatLanguageModel(model);

        TranslateUtil translateUtil = aiServices.build();
        return translateUtil.translateText(content, sourceLanguage, targetLanguage);
    }

//    public void translate(String recordId, Paragraph paragraph, String targetLanguage) {
//        if (paragraph.getText().trim().length() < 4) {
//            return;
//        }
//
//        AiServices<TranslateUtil> aiServices = AiServices.builder(TranslateUtil.class)
//                .chatMemoryProvider(memoryId -> MessageWindowChatMemory.withMaxMessages(30))
//                .chatLanguageModel(model);
//
//        TranslateUtil translateUtil = aiServices.build();
////        TranslateUtil translateUtil = AiServices.create(TranslateUtil.class, model);
////        ChatMemory chatMemory = MessageWindowChatMemory.withMaxMessages(10);
////        translateUtil.chatMemory(chatMemory);
//        String translatedText = translateUtil.translate(recordId, paragraph.getText(), glossary.toString(), targetLanguage);
//        paragraph.setTranslatedText(translatedText.replace("(", "（").replace(")", "）").replace(".", "·").replace("/", " ").replace("\\u0004", " "));
//    }

    /**
     * 翻译页面片段
     * @param recordId
     * @param fullText 页面全文，用于结合上下文
     * @param text 待翻译片段
     * @param language
     * @return
     */
    public String translatePage(String recordId, String fullText, String text, String language) {
        AiServices<TranslateUtil> aiServices = AiServices.builder(TranslateUtil.class)
                .chatMemoryProvider(memoryId -> MessageWindowChatMemory.withMaxMessages(50))
                .chatLanguageModel(model);
        TranslateUtil translateUtil = aiServices.build();
        return translateUtil.translatePage(recordId, fullText, text, language);
    }

}
