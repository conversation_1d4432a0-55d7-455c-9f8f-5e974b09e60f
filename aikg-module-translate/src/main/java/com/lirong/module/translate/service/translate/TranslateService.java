package com.lirong.module.translate.service.translate;

import com.lirong.module.translate.controller.admin.record.vo.RecordRespVO;
import com.lirong.module.translate.controller.admin.tanslate.vo.TranslateReqVO;
import com.lirong.module.translate.dal.dataobject.record.RecordDO;

public interface TranslateService {

    /**
     * 翻译文献
     * @param translateReqVO
     * @return
     */
    RecordRespVO translateDoc(TranslateReqVO translateReqVO);

    /**
     * 翻译文献
     * @param recordDO
     */
    void translateRecord(RecordDO recordDO);

    void translateRecordNew(RecordDO recordDO);

    /**
     * 翻译文献（新版）
     * @param recordDO
     */
    void translateRecordV2(RecordDO recordDO);

    /**
     * 翻译文本
     * @param translateReqVO
     * @return
     */
    String translateText(TranslateReqVO translateReqVO);
}
