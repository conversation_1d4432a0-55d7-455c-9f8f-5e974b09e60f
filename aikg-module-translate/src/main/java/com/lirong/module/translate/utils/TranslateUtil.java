package com.lirong.module.translate.utils;

import dev.langchain4j.service.MemoryId;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

public interface TranslateUtil {

    /**
     * 翻译
     *
     * @param text     文本
     * @param glossary 术语表
     * @param language 语言
     * @return 翻译结果
     */
    @SystemMessage(fromResource = "/prompt/translator-doc-system-prompt-template.txt")
    @UserMessage(fromResource = "/prompt/translator-user-prompt-template.txt")
    String translate(@MemoryId String id, @V("text") String text, @V("glossary") String glossary, @V("language") String language);


    /**
     * 翻译文本
     * @param text
     * @param sourceLanguage
     * @param targetLanguage
     * @return
     */
    @SystemMessage(fromResource = "/prompt/translator-text-system-prompt-template.txt")
    @UserMessage(fromResource = "/prompt/translator-user-prompt-template.txt")
    String translateText(@V("text") String text, @V("sourceLanguage") String sourceLanguage, @V("targetLanguage") String targetLanguage);

    /**
     * 结合文档全文翻译文档片段
     * @param id
     * @param fullText
     * @param text
     * @param language
     * @return
     */
    @SystemMessage(fromResource = "/prompt/translator-page-system-prompt-template.txt")
    @UserMessage(fromResource = "/prompt/translator-user-prompt-template.txt")
    String translatePage(@MemoryId String id, @V("fullText") String fullText, @V("text") String text, @V("language") String language);
}
