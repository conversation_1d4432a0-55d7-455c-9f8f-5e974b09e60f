package com.lirong.module.translate.pdfbox;

import lombok.Builder;
import lombok.Data;

import java.awt.*;

@Data
@Builder
public class Rect {
    private Rectangle bounds;

    private Color strokingColor;
    private Color nonStrokingColor;

//    public void  equals()

    public boolean equals(Rect rect) {
        return this.bounds.x == rect.bounds.x
                && this.bounds.y == rect.bounds.y
                && this.bounds.width == rect.bounds.width
                && this.bounds.height == rect.bounds.height
                && this.strokingColor.getBlue() == rect.strokingColor.getBlue()
                && this.strokingColor.getRed() == rect.strokingColor.getRed()
                && this.strokingColor.getGreen() == rect.strokingColor.getGreen()
                && this.nonStrokingColor.getBlue() == rect.nonStrokingColor.getBlue()
                && this.nonStrokingColor.getRed() == rect.nonStrokingColor.getRed()
                && this.nonStrokingColor.getGreen() == rect.nonStrokingColor.getGreen()
                ;
    }
}
