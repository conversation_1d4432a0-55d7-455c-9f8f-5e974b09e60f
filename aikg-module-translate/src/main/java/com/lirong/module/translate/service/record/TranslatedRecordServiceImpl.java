package com.lirong.module.translate.service.record;

import com.lirong.framework.security.core.util.SecurityFrameworkUtils;
import com.lirong.module.aigc.api.DocApi;
import com.lirong.module.aigc.api.dto.DocRespDTO;
import com.lirong.module.translate.controller.admin.tanslate.vo.TranslateReqVO;
import com.lirong.module.translate.service.translate.TranslateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import com.lirong.module.translate.controller.admin.record.vo.*;
import com.lirong.module.translate.dal.dataobject.record.RecordDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.translate.dal.mysql.record.RecordMapper;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.translate.enums.ErrorCodeConstants.*;

/**
 * 翻译记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class TranslatedRecordServiceImpl implements TranslatedRecordService {

    @Resource
    private TranslateService translateService;

    @Resource
    private RecordMapper recordMapper;
    @Resource
    private DocApi docApi;


    @Override
    public Long createRecord(RecordSaveReqVO createReqVO) {
        // 插入
        RecordDO record = BeanUtils.toBean(createReqVO, RecordDO.class);
        record.setCreator(SecurityFrameworkUtils.getLoginUserId().toString());
        record.setCreateTime(LocalDateTime.now());
        recordMapper.insert(record);
        // 返回
        return record.getId();
    }

    @Override
    public void updateRecord(RecordSaveReqVO updateReqVO) {
        // 校验存在
        validateRecordExists(updateReqVO.getId());
        // 更新
        RecordDO updateObj = BeanUtils.toBean(updateReqVO, RecordDO.class);
        recordMapper.updateById(updateObj);
    }

    @Override
    public void updateCompleteRecord(RecordDO recordDO) {
        List<RecordDO> recordDOS = recordMapper.selectList(RecordDO::getMd5, recordDO.getMd5(),
                RecordDO::getTargetLanguage, recordDO.getTargetLanguage());
        recordDOS.stream().filter(record -> record.getStatus() == 1).forEach(record -> {
            record.setStatus(2);
            record.setTarget(recordDO.getTarget());
            record.setPercent(100D);
            record.setSpent(recordDO.getSpent());
            recordMapper.updateById(record);
        });
    }

    @Override
    public void deleteRecord(Long id) {
        // 校验存在
        validateRecordExists(id);
        // 删除
        recordMapper.deleteById(id);
    }

    private void validateRecordExists(Long id) {
        if (recordMapper.selectById(id) == null) {
            throw exception(RECORD_NOT_EXISTS);
        }
    }

    @Override
    public RecordDO getRecord(Long id) {
        return recordMapper.selectById(id);
    }

    @Override
    public RecordDO getRecordByDocId(Long docId) {
        List<RecordDO> recordDOList = recordMapper.selectList(RecordDO::getSourceId, docId);
        if (null == recordDOList || recordDOList.isEmpty()) {
            DocRespDTO doc = docApi.getDoc(docId);
            RecordDO recordDO = new RecordDO();
            recordDO.setSourceLanguage("English");
            recordDO.setSourceId(docId);
            recordDO.setSource(doc.getContent());
            recordDO.setFileName(doc.getName());
            recordDO.setTargetLanguage("Simplified Chinese");
            recordDO.setStatus(0);
            recordDO.setPercent(0d);
            recordDO.setCreator(SecurityFrameworkUtils.getLoginUserId().toString());
            recordDO.setCreateTime(LocalDateTime.now());
            recordMapper.insert(recordDO);
            TranslateReqVO translateReqVO = BeanUtils.toBean(recordDO, TranslateReqVO.class);
            translateReqVO.setRecordId(recordDO.getId());
            translateReqVO.setContent(recordDO.getSource());
            translateService.translateDoc(translateReqVO);
            return recordDO;
        }
        return recordDOList.stream().findFirst().orElse(null);
    }

    @Override
    public PageResult<RecordDO> getRecordPage(RecordPageReqVO pageReqVO) {
        return recordMapper.selectPage(pageReqVO);
    }

    @Override
    public RecordDO getRecordByMD5(String md5) {
        List<RecordDO> recordDOS = recordMapper.selectList(RecordDO::getMd5, md5);
        return recordDOS.stream().findFirst().orElse(null);
    }

    @Override
    public RecordDO getRecordByMD5AndLang(String md5, String lang) {
        List<RecordDO> recordDOS = recordMapper.selectList(RecordDO::getMd5, md5, RecordDO::getTargetLanguage, lang);
        return recordDOS.stream()
                .filter(recordDO -> StringUtils.isNotBlank(recordDO.getTarget()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<RecordDO> getUnTranslatedRecords() {
        return recordMapper.selectList(RecordDO::getStatus, 1);
    }

    @Override
    public List<RecordDO> getTranslatedRecords(RecordPageReqVO pageReqVO) {
        recordMapper.selectTranslatedRecords(pageReqVO);
        return List.of();
    }

}