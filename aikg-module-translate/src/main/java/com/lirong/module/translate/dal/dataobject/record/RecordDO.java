package com.lirong.module.translate.dal.dataobject.record;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.lirong.framework.mybatis.core.dataobject.BaseDO;

/**
 * 翻译记录 DO
 *
 * <AUTHOR>
 */
@TableName("trans_record")
@KeySequence("trans_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 源语言
     */
    private String sourceLanguage;
    /**
     * 源编号
     */
    private Long sourceId;
    /**
     * 源
     */
    private String source;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件MD5
     */
    private String md5;
    /**
     * 目标语言
     */
    private String targetLanguage;
    /**
     * 目标
     */
    private String target;
    /**
     * 进度
     */
    private Double percent;

    /**
     * 耗时
     */
    private Long spent;

    /**
     * 状态
     */
    private Integer status;

}