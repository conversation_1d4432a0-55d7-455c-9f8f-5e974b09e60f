package com.lirong.module.translate.translator;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

//import com.lirong.module.translate.pdfbox.Paragraph;
//import com.lirong.module.translate.task.TranslateTask;


public class ParagraphTranslator {

    private Translator translator;
    private String targetLanguage;
    private String recordId;

    public ParagraphTranslator(String recordId, Translator translator, String targetLanguage) {
        this.translator = translator;
        this.targetLanguage = targetLanguage;
        this.recordId = recordId;
    }

//    /**
//     * 翻译Paragraphs
//     * @param paragraphs 段落
//     * @throws InterruptedException
//     */
//    public void translateParagraphs(List<Paragraph> paragraphs) throws InterruptedException {
//        // 创建固定大小的线程池
//        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
//
//
//        // 提交任务到线程池
////        List<Future<?>> futures = new ArrayList<>();
////        for (Paragraph paragraph : paragraphs) {
////            Future<?> future = executor.submit(new TranslateTask(paragraph, translator));
////            futures.add(future);
////        }
//
//        for (Paragraph paragraph : paragraphs) {
//            // 为每个Paragraph创建一个TranslateTask并提交给线程池
//            executor.execute(new TranslateTask(recordId, paragraph, targetLanguage, translator));
//        }
//
//        // 关闭线程池
//        executor.shutdown();
//        // 等待所有任务完成
//        executor.awaitTermination(1, TimeUnit.HOURS);
//
//        // 检查是否有任务失败
////        for (Future<?> future : futures) {
////            try {
////                future.get(); // 获取结果，如果有异常会抛出
////            } catch (ExecutionException e) {
////                // 处理任务执行中的异常
////                e.getCause().printStackTrace();
////            }
////        }
//    }

}
