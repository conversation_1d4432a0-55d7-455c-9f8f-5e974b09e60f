package com.lirong.module.intelligence.service.source;

import javax.validation.*;

import com.lirong.module.intelligence.controller.admin.source.vo.SourcePageReqVO;
import com.lirong.module.intelligence.controller.admin.source.vo.SourceSaveReqVO;
import com.lirong.module.intelligence.dal.dataobject.source.SourceDO;
import com.lirong.framework.common.pojo.PageResult;

/**
 * 采集源 Service 接口
 *
 * <AUTHOR>
 */
public interface SourceService {

    /**
     * 创建采集源
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSource(@Valid SourceSaveReqVO createReqVO);

    /**
     * 更新采集源
     *
     * @param updateReqVO 更新信息
     */
    void updateSource(@Valid SourceSaveReqVO updateReqVO);

    /**
     * 删除采集源
     *
     * @param id 编号
     */
    void deleteSource(Long id);

    /**
     * 获得采集源
     *
     * @param id 编号
     * @return 采集源
     */
    SourceDO getSource(Long id);

    /**
     * 根据域名获取采集源
     * @param domain
     * @return
     */
    SourceDO selectByDomain(String domain);

    /**
     * 获得采集源分页
     *
     * @param pageReqVO 分页查询
     * @return 采集源分页
     */
    PageResult<SourceDO> getSourcePage(SourcePageReqVO pageReqVO);

}