package com.lirong.module.intelligence.controller.admin.source.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 采集源 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SourceRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13068")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "国家")
    @ExcelProperty("国家")
    private String country;

    @Schema(description = "简称", example = "张三")
    @ExcelProperty("简称")
    private String shortName;

    @Schema(description = "名称（中文）", example = "王五")
    @ExcelProperty("名称（中文）")
    private String sourceName;

    @Schema(description = "名称（英文）")
    @ExcelProperty("名称（英文）")
    private String sourceNameEn;

    @Schema(description = "网站类型", example = "1")
    @ExcelProperty("网站类型")
    private String type;

    @Schema(description = "网站logo")
    @ExcelProperty("网站logo")
    private String logo;

    @Schema(description = "域名")
    @ExcelProperty("域名")
    private String domain;

    @Schema(description = "简介")
    @ExcelProperty("简介")
    private String introduction;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}