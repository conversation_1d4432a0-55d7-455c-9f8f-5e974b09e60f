<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lirong.boot</groupId>
        <artifactId>aikg-module-intelligence</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aikg-module-intelligence-biz</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-intelligence-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

    </dependencies>

</project>