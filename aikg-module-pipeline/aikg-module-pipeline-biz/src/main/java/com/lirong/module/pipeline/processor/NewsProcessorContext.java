package com.lirong.module.pipeline.processor;

import com.lirong.module.pipeline.api.es.dto.NewsMessage;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class NewsProcessorContext {

    private List<NewsProcessingStrategy> strategies;

    @Autowired
    public NewsProcessorContext(List<NewsProcessingStrategy> strategies) {
        this.strategies = strategies;
    }

    public CompletableFuture<NewsMessage> executeStrategiesAsync(NewsMessage message) {
        // 执行所有策略并行处理
        List<CompletableFuture<NewsMessage>> futures = strategies.stream()
                .map(strategy -> strategy.processAsync(message)
                        .exceptionally(ex -> {
                            // 处理策略中的异常，这里可以记录日志或者执行其他异常处理逻辑
                            System.err.println("\n\n********************");
                            System.err.println("\nStrategy failed with exception: \n" + ex.getMessage());
                            System.err.println("\n");
                            System.err.println(JSON.toJSONString(message));
                            System.err.println("\n\n********************");
                            // 然后返回原始消息或者一个错误消息对象
                            return null; // 返回原始消息或者错误消息
                        }))
                .collect(Collectors.toList());

        // 合并所有Future并等待所有完成
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    // 检查是否有任何策略失败（返回null）
                    if (futures.stream().anyMatch(Objects::isNull)) {
                        // 如果有策略失败，返回null或其他错误状态
                        return null;
                    }
                    // 所有策略完成后，返回处理后的消息
                    return futures.stream()
                            .filter(Objects::nonNull) // 过滤掉失败的结果
                            .map(CompletableFuture::join)
                            .reduce((first, second) -> second)
                            .orElse(message);
                });
    }

}