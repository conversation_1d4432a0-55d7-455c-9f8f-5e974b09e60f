package com.lirong.module.pipeline.utils;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

public class URLUtils {

    /**
     * 根据给定的URL获取域名
     *
     * @param urlString URL地址
     * @return 域名，如果URL无效则返回null
     */
    public static String getDomainName(String urlString) {
        try {
            // 解析URL
            URL url = new URL(urlString);
            // 获取 authority，通常是域名:端口形式
            String authority = url.getAuthority();
            if (authority == null) {
                return null;
            }
            // 移除端口部分，如果存在
            return authority.substring(0, authority.indexOf(':') == -1 ? authority.length() : authority.indexOf(':'));
        } catch (Exception e) {
            // 如果URL格式不正确，捕获异常并返回null
            return null;
        }
    }

    public static void main(String[] args) {
        // 测试方法
        String url = "http://www.example.com:8080/path/to/resource";
        String domainName = getDomainName(url);
        if (domainName != null) {
            System.out.println("Domain Name: " + domainName);
        } else {
            System.out.println("Invalid URL or unable to extract domain name.");
        }
    }
}
