package com.lirong.module.pipeline.config;

import com.zhipu.oapi.ClientV4;
import okhttp3.ConnectionPool;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class AiClientConfig {

    @Value("${aigc.model.chat.base-url}")
    private String baseUrl;
    private String apiSecretKey = "a.b"; //

    @Bean
    public ClientV4 clientV4() {
        return new ClientV4.Builder(baseUrl, apiSecretKey)
                .networkConfig(300, 100, 100, 100, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(8, 1, TimeUnit.SECONDS))
                .build();
    }

}
