<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lirong.boot</groupId>
        <artifactId>aikg-module-pipeline</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aikg-module-pipeline-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        pipeline 模块
    </description>

    <dependencies>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-pipeline-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lirong.boot</groupId>
            <artifactId>aikg-spring-boot-starter-biz-ip</artifactId>
        </dependency>



        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bigmodel.openapi</groupId>
            <artifactId>oapi-java-sdk</artifactId>
        </dependency>



    </dependencies>

</project>