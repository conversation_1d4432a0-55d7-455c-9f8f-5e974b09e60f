package com.lirong.module.organization.service.organization;

import javax.validation.*;
import com.lirong.module.organization.controller.admin.organization.vo.*;
import com.lirong.module.organization.dal.dataobject.organization.OrganizationDO;
import com.lirong.framework.common.pojo.PageResult;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface OrganizationService {

    /**
     * 创建机构
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid OrganizationSaveReqVO createReqVO);

    /**
     * 更新机构
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid OrganizationSaveReqVO updateReqVO);

    /**
     * 删除机构
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 获得机构
     *
     * @param id 编号
     * @return 机构
     */
    OrganizationDO get(Long id);

    /**
     * 获得机构分页
     *
     * @param pageReqVO 分页查询
     * @return 机构分页
     */
    PageResult<OrganizationDO> getPage(OrganizationPageReqVO pageReqVO);

}