/*
 *   Copyright 2020 Goldman Sachs.
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing,
 *   software distributed under the License is distributed on an
 *   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 *   KIND, either express or implied.  See the License for the
 *   specific language governing permissions and limitations
 *   under the License.
 */

package com.lirong.module.parser.model.attribute;

import com.lirong.module.parser.model.Attribute;
import com.lirong.module.parser.model.Element;
import com.lirong.module.parser.model.Form;

/**
 * Attribute defined for form data of the element
 */
public class FormData extends Attribute<Form> {

  private static final long serialVersionUID = 8836354727614958912L;

  public FormData(Form value) {
    this.setValue(value);
  }

  @Override
  public Class getHolderInterface() {
    return Holder.class;
  }

  public interface Holder<E extends Element> {

    default E add(FormData attribute) {
      E element = (E) this;
      element.addAttribute(attribute);
      return element;
    }

    default FormData getFormData() {
      E element = (E) this;
      return element.getAttribute(FormData.class);
    }
  }
}
