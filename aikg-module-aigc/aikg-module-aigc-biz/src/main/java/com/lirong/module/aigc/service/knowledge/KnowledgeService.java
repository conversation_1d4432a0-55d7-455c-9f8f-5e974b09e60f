package com.lirong.module.aigc.service.knowledge;

import javax.validation.*;

import com.lirong.module.aigc.controller.admin.knowledge.vo.KnowledgePageReqVO;
import com.lirong.module.aigc.controller.admin.knowledge.vo.KnowledgeSaveReqVO;
import com.lirong.module.aigc.controller.admin.knowledge.vo.KnowledgeTreeVO;
import com.lirong.module.aigc.dal.dataobject.knowledge.KnowledgeDO;
import com.lirong.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 知识库 Service 接口
 *
 * <AUTHOR>
 */
public interface KnowledgeService {

    /**
     * 创建知识库
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createKnowledge(@Valid KnowledgeSaveReqVO createReqVO);

    /**
     * 更新知识库
     *
     * @param updateReqVO 更新信息
     */
    void updateKnowledge(@Valid KnowledgeSaveReqVO updateReqVO);

    /**
     * 删除知识库
     *
     * @param id 编号
     */
    void deleteKnowledge(Long id);

    /**
     * 获得知识库
     *
     * @param id 编号
     * @return 知识库
     */
    KnowledgeDO getKnowledge(Long id);

    /**
     * 获得知识库分页
     *
     * @param pageReqVO 分页查询
     * @return 知识库分页
     */
    PageResult<KnowledgeDO> getKnowledgePage(KnowledgePageReqVO pageReqVO);

    /**
     * 获取知识库
     *
     * @param userId
     * @return
     */
    List<KnowledgeDO> listKnowledge(Long userId);

    /**
     * 获取知识库树
     *
     * @return 知识库树
     */
    public List<KnowledgeTreeVO> getKnowledgeTree();
}