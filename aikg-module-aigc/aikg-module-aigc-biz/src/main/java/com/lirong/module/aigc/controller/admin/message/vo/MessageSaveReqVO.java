package com.lirong.module.aigc.controller.admin.message.vo;

import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 对话消息新增/修改 Request VO")
@Data
public class MessageSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24063")
    private Long id;

    @Schema(description = "用户ID", example = "30219")
    private Long userId;

    @Schema(description = "会话ID", example = "21790")
    private Long conversationId;

    @Schema(description = "应用ID", example = "984")
    private Long promptId;

    @Schema(description = "消息的ID", example = "25582")
    private String chatId;

    @Schema(description = "用户名", example = "李四")
    private String username;

    @Schema(description = "IP地址")
    private String ip;

    @Schema(description = "角色，user和assistant")
    private String role;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "消息内容")
    private String message;

    @Schema(description = "Tokens")
    private Integer tokens;

    @Schema(description = "提示词Tokens")
    private Integer promptTokens;

    private String sources;

    private String extensionResult;

    private String historyPreview;

}