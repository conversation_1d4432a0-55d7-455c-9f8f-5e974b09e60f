package com.lirong.module.aigc.controller.admin.model;

import com.lirong.module.aigc.controller.admin.model.vo.ModelPageReqVO;
import com.lirong.module.aigc.controller.admin.model.vo.ModelRespVO;
import com.lirong.module.aigc.controller.admin.model.vo.ModelSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.pojo.CommonResult;
import com.lirong.framework.common.util.object.BeanUtils;
import static com.lirong.framework.common.pojo.CommonResult.success;

import com.lirong.module.aigc.dal.dataobject.model.ModelDO;
import com.lirong.module.aigc.service.model.ModelService;

@Tag(name = "管理后台 - 模型配置")
@RestController
@RequestMapping("/aigc/model")
@Validated
public class ModelController {

    @Resource
    private ModelService modelService;

    @PostMapping("/create")
    @Operation(summary = "创建模型配置")
    @PreAuthorize("@ss.hasPermission('aigc:model:create')")
    public CommonResult<Long> createModel(@Valid @RequestBody ModelSaveReqVO createReqVO) {
        return success(modelService.createModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新模型配置")
    @PreAuthorize("@ss.hasPermission('aigc:model:update')")
    public CommonResult<Boolean> updateModel(@Valid @RequestBody ModelSaveReqVO updateReqVO) {
        modelService.updateModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除模型配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('aigc:model:delete')")
    public CommonResult<Boolean> deleteModel(@RequestParam("id") Long id) {
        modelService.deleteModel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得模型配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('aigc:model:query')")
    public CommonResult<ModelRespVO> getModel(@RequestParam("id") Long id) {
        ModelDO model = modelService.getModel(id);
        return success(BeanUtils.toBean(model, ModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得模型配置分页")
    @PreAuthorize("@ss.hasPermission('aigc:model:query')")
    public CommonResult<PageResult<ModelRespVO>> getModelPage(@Valid ModelPageReqVO pageReqVO) {
        PageResult<ModelDO> pageResult = modelService.getModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelRespVO.class));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出模型配置 Excel")
//    @PreAuthorize("@ss.hasPermission('aigc:model:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportModelExcel(@Valid ModelPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<ModelDO> list = modelService.getModelPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "模型配置.xls", "数据", ModelRespVO.class,
//                        BeanUtils.toBean(list, ModelRespVO.class));
//    }

}