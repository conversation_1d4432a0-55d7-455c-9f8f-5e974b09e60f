package com.lirong.module.aigc.controller.admin.datasync.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 数据同步新增/修改 Request VO")
@Data
public class DataSyncSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15629")
    private Long id;

    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3292")
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    @Schema(description = "上级ID", example = "23785")
    private Long parentId;

    @Schema(description = "目录", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "目录不能为空")
    private String catalogue;

    @Schema(description = "文件总数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件总数不能为空")
    private Integer sum;

    @Schema(description = "上传数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "14483")
    @NotNull(message = "上传数量不能为空")
    private Integer uploadCount;

    @Schema(description = "上传状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上传状态")
    private Short uploadStatus;

    @Schema(description = "创建子目录", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "创建子目录不能为空")
    private Short createDirectory;

}