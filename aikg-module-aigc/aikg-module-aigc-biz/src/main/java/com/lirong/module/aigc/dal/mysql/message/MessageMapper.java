package com.lirong.module.aigc.dal.mysql.message;

import java.util.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.aigc.dal.dataobject.message.MessageDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.aigc.controller.admin.message.vo.*;

/**
 * 对话消息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageMapper extends BaseMapperX<MessageDO> {

    default PageResult<MessageDO> selectPage(MessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MessageDO>()
                .eqIfPresent(MessageDO::getUserId, reqVO.getUserId())
                .eqIfPresent(MessageDO::getConversationId, reqVO.getConversationId())
                .eqIfPresent(MessageDO::getPromptId, reqVO.getPromptId())
                .eqIfPresent(MessageDO::getChatId, reqVO.getChatId())
                .likeIfPresent(MessageDO::getUsername, reqVO.getUsername())
                .eqIfPresent(MessageDO::getIp, reqVO.getIp())
                .eqIfPresent(MessageDO::getRole, reqVO.getRole())
                .eqIfPresent(MessageDO::getModel, reqVO.getModel())
                .eqIfPresent(MessageDO::getMessage, reqVO.getMessage())
                .eqIfPresent(MessageDO::getTokens, reqVO.getTokens())
                .eqIfPresent(MessageDO::getPromptTokens, reqVO.getPromptTokens())
                .betweenIfPresent(MessageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MessageDO::getId));
    }

}