package com.lirong.module.aigc.controller.admin.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 模型配置新增/修改 Request VO")
@Data
public class ModelSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "480")
    private Long id;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "供应商")
    private String provider;

    @Schema(description = "别名", example = "赵六")
    private String name;

    @Schema(description = "响应长度")
    private Integer responseLimit;

    @Schema(description = "温度")
    private Double temperature;

    @Schema(description = "TOP_P")
    private Double topP;

    @Schema(description = "服务地址", example = "https://www.iocoder.cn")
    private String baseUrl;

    @Schema(description = "API_KEY")
    private String apiKey;

    @Schema(description = "SECRET_KEY")
    private String secretKey;

    @Schema(description = "END_POINT")
    private String endpoint;

    @Schema(description = "向量维数")
    private Integer dimensions;

    @Schema(description = "类型: CHAT、Embedding", example = "1")
    private String type;

}