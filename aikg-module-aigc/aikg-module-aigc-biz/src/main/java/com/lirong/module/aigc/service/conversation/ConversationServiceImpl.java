package com.lirong.module.aigc.service.conversation;

import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import com.lirong.module.aigc.service.docs.DocsService;
import com.lirong.module.aigc.service.message.MessageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import com.lirong.module.aigc.controller.admin.conversation.vo.*;
import com.lirong.module.aigc.dal.dataobject.conversation.ConversationDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.aigc.dal.mysql.conversation.ConversationMapper;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.aigc.enums.ErrorCodeConstants.*;

/**
 * 对话窗口 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConversationServiceImpl implements ConversationService {

    @Resource
    private ConversationMapper conversationMapper;
    @Resource
    private MessageService messageService;
    @Resource
    private DocsService docsService;

    @Override
    public ConversationRespVO createConversation(ConversationSaveReqVO createReqVO) {
        // 插入
        ConversationDO conversation = BeanUtils.toBean(createReqVO, ConversationDO.class);
        String[] split = conversation.getTitle().split("\n");
        if (split[0].length() > 200) {
            conversation.setTitle(split[0].substring(0, 200));
        } else {
            conversation.setTitle(split[0]);
        }
        conversationMapper.insert(conversation);
        // 返回
        return BeanUtils.toBean(conversation, ConversationRespVO.class);
    }

    @Override
    public void updateConversation(ConversationSaveReqVO updateReqVO) {
        // 校验存在
        validateConversationExists(updateReqVO.getId());
        // 更新
        ConversationDO updateObj = BeanUtils.toBean(updateReqVO, ConversationDO.class);
        conversationMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteConversation(Long id) {
        // 校验存在
        validateConversationExists(id);
        // 删除会话消息
        messageService.deleteMessageByConversation(id);
        // 删除会话
        conversationMapper.deleteById(id);
    }

    private void validateConversationExists(Long id) {
        if (conversationMapper.selectById(id) == null) {
            throw exception(CONVERSATION_NOT_EXISTS);
        }
    }

    @Override
    public ConversationDO getConversation(Long id) {
        ConversationDO conversationDO = conversationMapper.selectById(id);
        if (null != conversationDO.getDocId()) {
            DocsDO docs = docsService.getDocs(conversationDO.getDocId());
            if (null != docs) {
                conversationDO.setFileName(docs.getContent());
            }
        }
        return conversationDO;
    }

    @Override
    public PageResult<ConversationDO> getConversationPage(ConversationPageReqVO pageReqVO) {
        return conversationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ConversationDO> getAllConversation(Long userId, String type) {
        return conversationMapper.selectList(userId, type);
    }

    @Override
    public void updateKnowledgeId(Long conversationId, Long knowledgeId) {
        conversationMapper.updateKnowledgeId(conversationId, knowledgeId);
    }

}