package com.lirong.module.aigc.controller.admin.chat;

import cn.hutool.core.util.StrUtil;
import com.lirong.framework.security.core.util.SecurityFrameworkUtils;
import com.lirong.module.aigc.core.dto.ChatReq;
import com.lirong.module.aigc.core.service.ChatService;
import com.lirong.module.aigc.core.utils.PromptUtil;
import com.lirong.module.aigc.core.utils.StreamEmitter;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RequestMapping("/aigc")
@RestController
@AllArgsConstructor
public class ChatController {

    private final ChatService chatService;

    @PostMapping("/chat/completions")
    public Object chatCompletions(@RequestBody ChatReq req, HttpServletRequest request) {
        StreamEmitter emitter = new StreamEmitter();
        req.setEmitter(emitter);
        req.setUserId(SecurityFrameworkUtils.getLoginUserId());
        req.setUsername(SecurityFrameworkUtils.getLoginUserNickname());

        if ("knowledge".equals(req.getType()) || null != req.getKnowledgeId() || null != req.getDocsId()) {
            req.setPrompt(PromptUtil.buildDocs(req.getMessage()));
            chatService.chatKnowledge(req, request);
        } else if (StrUtil.isNotBlank(req.getPromptId())) {
            req.setPrompt(PromptUtil.build(req.getMessage(), req.getPromptText()));
            chatService.chat(req, request);
        } else {
            req.setPrompt(PromptUtil.build(req.getMessage()));
            chatService.chat(req, request);
        }
        return emitter.get();
    }

}
