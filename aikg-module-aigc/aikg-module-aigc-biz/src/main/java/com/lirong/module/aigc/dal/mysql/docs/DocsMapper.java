package com.lirong.module.aigc.dal.mysql.docs;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.aigc.controller.admin.docs.vo.DocsPageReqVO;
import com.lirong.module.aigc.controller.admin.knowledge.vo.KnowledgeTreeVO;
import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DocsMapper extends BaseMapperX<DocsDO> {

    default PageResult<DocsDO> selectPage(DocsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DocsDO>()
                .likeIfPresent(DocsDO::getName, reqVO.getName())
                .eqIfPresent(DocsDO::getKnowledgeId, reqVO.getKnowledgeId())
                .eqIfPresent(DocsDO::getParentId, reqVO.getParentId())
                .isNull(DocsDO::getParentId, reqVO.getParentId())
                .eqIfPresent(DocsDO::getType, reqVO.getType())
                .eqIfPresent(DocsDO::getSize, reqVO.getSize())
                .eqIfPresent(DocsDO::getSliceNum, reqVO.getSliceNum())
                .eqIfPresent(DocsDO::getSliceStatus, reqVO.getSliceStatus())
                .orderByAsc(DocsDO::getId));
    }

    /**
     * 根据文档ID更新索引状态
     *
     * @param id    ID
     * @param index 索引状态
     * @return 结果
     */
    public int updateDocsIndexById(@Param("id") Long id, @Param("index") int index);

    /**
     * 获取知识库文件夹树
     *
     * @return 知识库文件夹树
     */
    public List<KnowledgeTreeVO> selectDocsFolderTree();

    /**
     * 根据ID获取子集文件夹
     *
     * @param knowledgeId 知识库主键
     * @param id          主键
     * @return 结果
     */
    public List<DocsDO> selectChildrenFolderById(@Param("knowledgeId") Long knowledgeId, @Param("id") Long id);
}