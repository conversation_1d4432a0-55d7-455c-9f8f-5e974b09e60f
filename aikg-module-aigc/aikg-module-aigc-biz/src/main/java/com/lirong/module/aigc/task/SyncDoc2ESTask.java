package com.lirong.module.aigc.task;

import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import com.lirong.module.aigc.service.docs.DocsService;
import com.lirong.module.aigc.service.pdf.PdfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class SyncDoc2ESTask implements CommandLineRunner {

    @Autowired
    private DocsService docsService;
    @Autowired
    private PdfService pdfService;

    @Override
    public void run(String... args) throws Exception {
        DocsDO docsDO = new DocsDO();
        docsDO.setIndex((short) 0);
        docsDO.setType("file");
        docsService.selectDocs(docsDO).forEach(doc -> {
            if (!doc.getDeleted()) {
                pdfService.sync2es(doc);
            }
        });
    }

}
