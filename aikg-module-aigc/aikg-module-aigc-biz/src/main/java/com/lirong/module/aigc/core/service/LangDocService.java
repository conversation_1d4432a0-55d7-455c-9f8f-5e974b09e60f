package com.lirong.module.aigc.core.service;

import com.lirong.module.aigc.core.dto.ChatReq;
import com.lirong.module.aigc.core.dto.EmbeddingR;
import dev.langchain4j.service.TokenStream;

import java.util.List;

public interface LangDocService {

    /**
     * 解析文本向量
     */
    EmbeddingR embeddingText(ChatReq req);

    /**
     * 解析文本文件向量
     */
    List<EmbeddingR> embeddingDocs(ChatReq req);

    /**
     * 知识库对话
     */
    AiStream chatKnowledge(ChatReq req);

    /**
     * 文档对话
     */
    AiStream chatDoc(ChatReq req);

}
