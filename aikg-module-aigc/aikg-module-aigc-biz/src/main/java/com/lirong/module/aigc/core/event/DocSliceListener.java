package com.lirong.module.aigc.core.event;

import com.lirong.module.aigc.core.task.process.DataProcessingService;
import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class DocSliceListener {

//    private final EmbeddingService embeddingService;

    @Resource
    private DataProcessingService processingService;

    @Async
    @EventListener(DocSliceEvent.class)
    public void sliceDoc(DocSliceEvent event) {
        DocsDO doc = (DocsDO) event.getSource();
        processingService.addDataToQueue(doc);
    }

}
