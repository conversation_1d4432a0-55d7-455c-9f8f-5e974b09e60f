package com.lirong.module.aigc.controller.admin.knowledge;

import com.lirong.framework.security.core.util.SecurityFrameworkUtils;
import com.lirong.module.aigc.controller.admin.knowledge.vo.*;
import com.lirong.module.aigc.core.service.EmbeddingService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.pojo.CommonResult;
import com.lirong.framework.common.util.object.BeanUtils;

import static com.lirong.framework.common.pojo.CommonResult.success;

import com.lirong.module.aigc.dal.dataobject.knowledge.KnowledgeDO;
import com.lirong.module.aigc.service.knowledge.KnowledgeService;

@Tag(name = "管理后台 - 知识库")
@RestController
@RequestMapping("/aigc/knowledge")
@Validated
public class KnowledgeController {

    @Resource
    private EmbeddingService embeddingService;
    @Resource
    private KnowledgeService knowledgeService;

    @PostMapping("/create")
    @Operation(summary = "创建知识库")
    @PreAuthorize("@ss.hasPermission('aigc:knowledge:create')")
    public CommonResult<Long> createKnowledge(@Valid @RequestBody KnowledgeSaveReqVO createReqVO) {
        return success(knowledgeService.createKnowledge(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新知识库")
    @PreAuthorize("@ss.hasPermission('aigc:knowledge:update')")
    public CommonResult<Boolean> updateKnowledge(@Valid @RequestBody KnowledgeSaveReqVO updateReqVO) {
        knowledgeService.updateKnowledge(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除知识库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('aigc:knowledge:delete')")
    public CommonResult<Boolean> deleteKnowledge(@RequestParam("id") Long id) {
        knowledgeService.deleteKnowledge(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得知识库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('aigc:knowledge:query')")
    public CommonResult<KnowledgeRespVO> getKnowledge(@RequestParam("id") Long id) {
        KnowledgeDO knowledge = knowledgeService.getKnowledge(id);
        return success(BeanUtils.toBean(knowledge, KnowledgeRespVO.class));
    }

    // Assuming you have a method to convert LocalDate to LocalDateTime at the start of the day
    public LocalDateTime startOfDay(LocalDate date) {
        return date.atStartOfDay();
    }

    // Assuming you have a method to convert LocalDate to LocalDateTime at the end of the day
    public LocalDateTime endOfDay(LocalDate date) {
        return date.atTime(LocalTime.MAX);
    }

    @GetMapping("/page")
    @Operation(summary = "获得知识库分页")
    @PreAuthorize("@ss.hasPermission('aigc:knowledge:query')")
    public CommonResult<PageResult<KnowledgeRespVO>> getKnowledgePage(@Valid KnowledgePageReqVO pageReqVO) {
        List<LocalDate> createTimeDates = pageReqVO.getCreateTime();
        if (createTimeDates != null && !createTimeDates.isEmpty()) {
            pageReqVO.setCreateTimeStart(startOfDay(createTimeDates.get(0)));
            pageReqVO.setCreateTimeEnd(endOfDay(createTimeDates.get(createTimeDates.size() - 1)));
        }

        PageResult<KnowledgeDO> pageResult = knowledgeService.getKnowledgePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, KnowledgeRespVO.class));
    }

    /**
     * 获取用户创建的知识库列表
     *
     * @return
     */
    @GetMapping("/list")
    public CommonResult<List<KnowledgeRespVO>> listKnowledge() {
        List<KnowledgeDO> knowledgeDOList = knowledgeService.listKnowledge(SecurityFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(knowledgeDOList, KnowledgeRespVO.class));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出知识库 Excel")
//    @PreAuthorize("@ss.hasPermission('aigc:knowledge:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportKnowledgeExcel(@Valid KnowledgePageReqVO pageReqVO, HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<KnowledgeDO> list = knowledgeService.getKnowledgePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "知识库.xls", "数据", KnowledgeRespVO.class, BeanUtils.toBean(list, KnowledgeRespVO.class));
//    }

    /**
     * 搜索知识库
     *
     * @param queryVO
     * @return
     */
    @GetMapping("/search")
    public CommonResult<?> searchKnowledge(KnowledgeQueryVO queryVO) {
        return success(embeddingService.search(queryVO));
    }

    /**
     * 搜索知识库文档
     *
     * @param content
     * @return
     */
    @GetMapping("/search-doc")
    public CommonResult<?> searchKnowledgeDoc(@RequestParam("content") String content) {
        return success(embeddingService.searchKnowledgeDoc(content));
    }

    /**
     * 获取知识库树
     */
    @GetMapping("/tree")
    @Operation(summary = "获取知识库树")
    public CommonResult<?> getTree() {
        return success(knowledgeService.getKnowledgeTree());
    }

}