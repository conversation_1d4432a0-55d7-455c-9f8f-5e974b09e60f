/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lirong.module.aigc.core.dto;

import com.alibaba.fastjson.JSONArray;
import dev.langchain4j.rag.content.Content;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/29
 */
@Data
@Accessors(chain = true)
public class ChatRes {

    private boolean isDone = false;

    private String message;

    private Integer usedToken;

    private long time;

    private List<Source> sources = new ArrayList<>();

    public ChatRes(String message) {
        this.message = message;
    }

    public ChatRes(List<Content> contents) {
        contents.forEach(content -> {
            this.sources.add(Source.builder().text(content.textSegment().text())
                    .matedata(content.textSegment().metadata().toMap()).build());

        });
    }

    public String getSources() {
        return JSONArray.toJSONString(sources);
    }

    public ChatRes(Integer usedToken, long startTime) {
        this.isDone = true;
        this.usedToken = usedToken;
        this.time = System.currentTimeMillis() - startTime;
    }
}
