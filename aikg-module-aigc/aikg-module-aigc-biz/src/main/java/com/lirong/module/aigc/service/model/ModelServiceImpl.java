package com.lirong.module.aigc.service.model;

import com.lirong.module.aigc.controller.admin.model.vo.ModelPageReqVO;
import com.lirong.module.aigc.controller.admin.model.vo.ModelSaveReqVO;
import com.lirong.module.aigc.core.consts.ModelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.lirong.module.aigc.dal.dataobject.model.ModelDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.aigc.dal.mysql.model.ModelMapper;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.aigc.enums.ErrorCodeConstants.*;

/**
 * 模型配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelServiceImpl implements ModelService {

    @Resource
    private ModelMapper modelMapper;

    @Override
    public Long createModel(ModelSaveReqVO createReqVO) {
        // 插入
        ModelDO model = BeanUtils.toBean(createReqVO, ModelDO.class);
        modelMapper.insert(model);
        // 返回
        return model.getId();
    }

    @Override
    public void updateModel(ModelSaveReqVO updateReqVO) {
        // 校验存在
        validateModelExists(updateReqVO.getId());
        // 更新
        ModelDO updateObj = BeanUtils.toBean(updateReqVO, ModelDO.class);
        modelMapper.updateById(updateObj);
    }

    @Override
    public void deleteModel(Long id) {
        // 校验存在
        validateModelExists(id);
        // 删除
        modelMapper.deleteById(id);
    }

    private void validateModelExists(Long id) {
        if (modelMapper.selectById(id) == null) {
            throw exception(MODEL_NOT_EXISTS);
        }
    }

    @Override
    public ModelDO getModel(Long id) {
        return modelMapper.selectById(id);
    }

    @Override
    public PageResult<ModelDO> getModelPage(ModelPageReqVO pageReqVO) {
        return modelMapper.selectPage(pageReqVO);
    }

    public List<ModelDO> listAllModel() {
        return modelMapper.selectList();
    }

    public List<ModelDO> getChatModels() {
        return modelMapper.selectList(Wrappers.<ModelDO>lambdaQuery()
                .eq(ModelDO::getType, ModelTypeEnum.CHAT.name()));
    }

    public List<ModelDO> getEmbeddingModels() {
        return modelMapper.selectList(Wrappers.<ModelDO>lambdaQuery()
                .eq(ModelDO::getType, ModelTypeEnum.EMBEDDING.name()));
    }

}