package com.lirong.module.aigc.core.publisher;

import com.lirong.module.aigc.core.event.DocSliceEvent;
import com.lirong.module.aigc.dal.dataobject.docs.DocsDO;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DocSlicePublisher {

    @Resource
    private ApplicationContext applicationContext;

    public void publishEvent(DocsDO docsDO) {
        applicationContext.publishEvent(new DocSliceEvent(docsDO));
    }

}
