package com.lirong.module.aigc.dal.dataobject.datasync;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.lirong.framework.mybatis.core.dataobject.BaseDO;

/**
 * 数据同步 DO
 *
 * <AUTHOR>
 */
@TableName("aigc_data_sync")
@KeySequence("aigc_data_sync_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSyncDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 知识库ID
     */
    private Long knowledgeId;
    /**
     * 上级ID
     */
    private Long parentId;
    /**
     * 目录
     */
    private String catalogue;
    /**
     * 文件总数
     */
    private Integer sum;
    /**
     * 上传数量
     */
    private Integer uploadCount;
    /**
     * 上传状态
     */
    private Short uploadStatus;
    /**
     * 创建子目录
     */
    private Short createDirectory;

}