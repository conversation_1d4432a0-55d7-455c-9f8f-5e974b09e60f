package com.lirong.module.aigc.controller.admin.message.vo;

import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import jodd.util.StringUtil;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 对话消息 Response VO")
@Data
public class MessageRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24063")
    private Long id;

    @Schema(description = "用户ID", example = "30219")
    private Long userId;

    @Schema(description = "会话ID", example = "21790")
    private Long conversationId;

    @Schema(description = "应用ID", example = "984")
    private Long promptId;

    @Schema(description = "消息的ID", example = "25582")
    private String chatId;

    @Schema(description = "用户名", example = "李四")
    private String username;

    @Schema(description = "IP地址")
    private String ip;

    @Schema(description = "角色，user和assistant")
    private String role;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "消息内容")
    private String message;

    @Schema(description = "Tokens")
    private Integer tokens;

    @Schema(description = "提示词Tokens")
    private Integer promptTokens;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    private String sources;

    public JSONArray getSources() {
        if (StringUtil.isNotBlank(sources)) {
            return JSONArray.parseArray(sources);
        } else {
            return new JSONArray();
        }
    }

}