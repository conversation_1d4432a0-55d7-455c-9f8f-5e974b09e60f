package com.lirong.module.aigc.controller.admin.docs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 文档 Response VO")
@Data
public class DocsRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24718")
    private Long id;

    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11478")
    private Long knowledgeId;

    @Schema(description = "名称", example = "赵六")
    private String name;

    @Schema(description = "标题", example = "赵六")
    private String title;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "缩略图")
    private String thumbnail;

    @Schema(description = "作者")
    private String author;

    @Schema(description = "发布时间")
    private String pubdate;

    @Schema(description = "来源")
    private String origin;

    @Schema(description = "文件大小")
    private Integer size;

    @Schema(description = "切片数量")
    private Integer sliceNum;

    @Schema(description = "切片状态", example = "1")
    private Boolean sliceStatus;

    @Schema(description = "内容或链接")
    private String content;

    @Schema(description = "原始")
    private String summary;

    @Schema(description = "原始摘要")
    private String rawSummary;

    @Schema(description = "语言", example = "中文")
    private String language;

    private String fileType;

    private String documentType;

    /**
     * 分片大小
     */
    private Integer chunkSize;

    public String getContent() {
        if (content == null) {
            return null;
        } else {
            return content.replace("*************", "*************");
        }
    }

    public String getFileType() {
        if (content == null) {
            return null;
        } else {
            return content.substring(content.lastIndexOf(".") + 1);
        }
    }

    public String getDocumentType() {
        if (getFileType() == null) {
            return null;
        } else if (getFileType().equals("doc") || getFileType().equals("docx")) {
            return "word";
        } else if (getFileType().equals("xls") || getFileType().equals("xlsx") || getFileType().equals("csv")) {
            return "cell";
        } else if (getFileType().equals("ppt") || getFileType().equals("pptx")) {
            return "slide";
        } else {
            return "pdf";
        }
    }

}