package com.lirong.module.aigc.task;

import com.lirong.module.aigc.dal.dataobject.datasync.DataSyncDO;
import com.lirong.module.aigc.service.datasync.DataSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DataSyncTask  implements CommandLineRunner {

    @Autowired
    DataSyncService dataSyncService;

    @Override
    public void run(String... args) throws Exception {
        List<DataSyncDO> unfinishedList = dataSyncService.getUnfinishedDataSync();
        for (DataSyncDO dataSyncDO : unfinishedList) {
            System.out.println("======== 同步目录 ========\n");
            System.out.println(dataSyncDO.getCatalogue());
            System.out.println("=========================\n");
            dataSyncService.syncDocs(dataSyncDO);
        }
    }

}
