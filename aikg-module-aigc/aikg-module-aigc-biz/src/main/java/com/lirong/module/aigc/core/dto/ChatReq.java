/*
 * Copyright (c) 2024 LangChat. TyCoding All Rights Reserved.
 *
 * Licensed under the GNU Affero General Public License, Version 3 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.gnu.org/licenses/agpl-3.0.html
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lirong.module.aigc.core.dto;

import com.lirong.module.aigc.core.utils.StreamEmitter;
import dev.langchain4j.model.input.Prompt;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Data
@Accessors(chain = true)
public class ChatReq {

    private String modelId;
    private String modelName;
    private String modelProvider;

    private String message;

    private Long conversationId;

    private Long userId;

    private String username;

    private String chatId;

    private String promptId;

    private String promptText;

    private String docsName;

    private Long knowledgeId;

    private Long parentId;

    private Long docsId;

    private String url;

    private String role;

    private Prompt prompt;

    private StreamEmitter emitter;

    private String type;

    private String extensionResult;

    private String historyPreview;
}
