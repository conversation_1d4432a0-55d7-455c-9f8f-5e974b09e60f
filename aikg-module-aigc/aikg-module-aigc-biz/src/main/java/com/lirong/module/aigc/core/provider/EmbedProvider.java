package com.lirong.module.aigc.core.provider;

import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.bgesmallenv15q.BgeSmallEnV15QuantizedEmbeddingModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

@Component
@AllArgsConstructor
public class EmbedProvider {

    private final Supplier<EmbeddingModel> embedModelSupplier;

    public static DocumentSplitter splitter() {
        return DocumentSplitters.recursive(700, 100);
    }

    public EmbedProvider() {
        this(() -> SingletonHolder.INSTANCE);
    }

    public EmbeddingModel embed() {
        return embedModelSupplier.get();
    }

    private static class SingletonHolder {
        private static final EmbeddingModel INSTANCE = new BgeSmallEnV15QuantizedEmbeddingModel();
//        private static final EmbeddingModel INSTANCE = LocalAiEmbeddingModel.builder()
//                .baseUrl("http://192.168.0.222:8000/v1/")
//                .modelName("test")
//                .logRequests(true)
//                .logResponses(true)
//                .build();
    }

}
