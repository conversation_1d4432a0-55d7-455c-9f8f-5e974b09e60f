package com.lirong.module.aigc.service.docsslice;

import com.lirong.module.aigc.controller.admin.docsslice.vo.DocsSlicePageReqVO;
import com.lirong.module.aigc.controller.admin.docsslice.vo.DocsSliceSaveReqVO;
import com.lirong.module.aigc.core.service.EmbeddingService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import com.lirong.module.aigc.dal.dataobject.docsslice.DocsSliceDO;
import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.common.util.object.BeanUtils;

import com.lirong.module.aigc.dal.mysql.docsslice.DocsSliceMapper;

import static com.lirong.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.lirong.module.aigc.enums.ErrorCodeConstants.*;

/**
 * 文档切片 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DocsSliceServiceImpl implements DocsSliceService {

    @Resource
    private DocsSliceMapper docsSliceMapper;
    @Lazy
    @Resource
    private EmbeddingService embeddingService;

    @Override
    public Long createDocsSlice(DocsSliceSaveReqVO createReqVO) {
        // 插入
        DocsSliceDO docsSlice = BeanUtils.toBean(createReqVO, DocsSliceDO.class);
        docsSliceMapper.insert(docsSlice);
        // 返回
        return docsSlice.getId();
    }

    @Override
    public Boolean addDocsSlice(List<DocsSliceSaveReqVO> createReqVOList) {
        // 返回
//        return createDocsSlice(createReqVO);
        return docsSliceMapper.insertBatch(BeanUtils.toBean(createReqVOList, DocsSliceDO.class));
    }

    @Override
    public void updateDocsSlice(DocsSliceSaveReqVO updateReqVO) {
        // 校验存在
        validateDocsSliceExists(updateReqVO.getId());
        // 更新
        DocsSliceDO updateObj = BeanUtils.toBean(updateReqVO, DocsSliceDO.class);
        docsSliceMapper.updateById(updateObj);
    }

    @Override
    @Transactional
    public void deleteDocsSlice(Long id) {
        DocsSliceDO docsSliceDO = docsSliceMapper.selectById(id);
        if (docsSliceDO == null) {
            // 删除向量
            embeddingService.removeEmbeddingById(docsSliceDO.getVectorId());
            // 删除
            docsSliceMapper.deleteById(id);
        }
    }

    @Transactional
    public void deleteDocsSliceByDocsId(Long docId) {
        // 删除向量
        embeddingService.removeEmbeddingByDoc(docId);
        // 根据文档删除切片
        docsSliceMapper.delete(DocsSliceDO::getDocsId, docId);
    }

    @Transactional
    public void deleteDocsSliceByKnowledge(Long knowledgeId) {
        // 根据知识库删除向量
        embeddingService.removeEmbeddingByKnowledge(knowledgeId);
        // 根据知识库删除切片
        docsSliceMapper.delete(DocsSliceDO::getKnowledgeId, knowledgeId);
    }

    private void validateDocsSliceExists(Long id) {
        if (docsSliceMapper.selectById(id) == null) {
            throw exception(DOCS_SLICE_NOT_EXISTS);
        }
    }

    @Override
    public DocsSliceDO getDocsSlice(Long id) {
        return docsSliceMapper.selectById(id);
    }

    @Override
    public PageResult<DocsSliceDO> getDocsSlicePage(DocsSlicePageReqVO pageReqVO) {
        return docsSliceMapper.selectPage(pageReqVO);
    }
}