package com.lirong.module.aigc.controller.admin.docs.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StringSerializer;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lirong.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 文档分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DocsPageReqVO extends PageParam {

    @Schema(description = "名称", example = "赵六")
    private String name;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "文件大小")
    private Integer size;

    @Schema(description = "切片数量")
    private Integer sliceNum;

    @Schema(description = "切片状态", example = "1")
    private Short sliceStatus;

    @JsonSerialize(using = StringSerializer.class)
    private Long parentId;

    @JsonSerialize(using = StringSerializer.class)
    private Long knowledgeId;

    @Schema(description = "检索属性")
    private String searchProperty;

    @Schema(description = "开始年")
    private String startYear;

    @Schema(description = "结束年")
    private String endYear;

    @Schema(description = "排序属性")
    private String sortProperty;

    // 检索方式：全文/语义
    private String searchType;

    /**
     * 分片大小
     */
    private Integer chunkSize;

}