package com.lirong.module.aigc.dal.mysql.conversation;

import java.util.*;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.aigc.dal.dataobject.conversation.ConversationDO;
import org.apache.ibatis.annotations.Mapper;
import com.lirong.module.aigc.controller.admin.conversation.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 对话窗口 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ConversationMapper extends BaseMapperX<ConversationDO> {

    default PageResult<ConversationDO> selectPage(ConversationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ConversationDO>().eqIfPresent(ConversationDO::getUserId, reqVO.getUserId()).eqIfPresent(ConversationDO::getPromptId, reqVO.getPromptId()).eqIfPresent(ConversationDO::getTitle, reqVO.getTitle()).eqIfPresent(ConversationDO::getType, reqVO.getType()).eqIfPresent(ConversationDO::getDocId, reqVO.getDocId()).betweenIfPresent(ConversationDO::getCreateTime, reqVO.getCreateTime()).orderByDesc(ConversationDO::getId));
    }

    List<ConversationDO> selectList(@Param("userId") Long userId, @Param("type") String type);

    ConversationDO selectById(@Param("conversationId") Long conversationId);

    /**
     * 更新knowledgeId
     * @param conversationId
     * @param knowledgeId
     */
    void updateKnowledgeId(@Param("conversationId") Long conversationId, @Param("knowledgeId") Long knowledgeId);

}