package com.lirong.module.aigc.dal.mysql.model;

import com.lirong.framework.common.pojo.PageResult;
import com.lirong.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lirong.framework.mybatis.core.mapper.BaseMapperX;
import com.lirong.module.aigc.controller.admin.model.vo.ModelPageReqVO;
import com.lirong.module.aigc.dal.dataobject.model.ModelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 模型配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelMapper extends BaseMapperX<ModelDO> {

    default PageResult<ModelDO> selectPage(ModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelDO>()
                .eqIfPresent(ModelDO::getModel, reqVO.getModel())
                .eqIfPresent(ModelDO::getProvider, reqVO.getProvider())
                .likeIfPresent(ModelDO::getName, reqVO.getName())
                .eqIfPresent(ModelDO::getResponseLimit, reqVO.getResponseLimit())
                .eqIfPresent(ModelDO::getTemperature, reqVO.getTemperature())
                .eqIfPresent(ModelDO::getTopP, reqVO.getTopP())
                .eqIfPresent(ModelDO::getBaseUrl, reqVO.getBaseUrl())
                .eqIfPresent(ModelDO::getApiKey, reqVO.getApiKey())
                .eqIfPresent(ModelDO::getSecretKey, reqVO.getSecretKey())
                .eqIfPresent(ModelDO::getEndpoint, reqVO.getEndpoint())
                .eqIfPresent(ModelDO::getDimensions, reqVO.getDimensions())
                .eqIfPresent(ModelDO::getType, reqVO.getType())
                .betweenIfPresent(ModelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelDO::getId));
    }

}