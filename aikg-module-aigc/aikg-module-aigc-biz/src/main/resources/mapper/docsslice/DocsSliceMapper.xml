<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.module.aigc.dal.mysql.docsslice.DocsSliceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="DocsSliceSearchVOResultMap" type="com.lirong.module.aigc.controller.admin.docs.vo.DocsSliceSearchVO">
        <id column="doc_id" property="docId" jdbcType="BIGINT"/>
        <result column="docs_name" property="docsName" jdbcType="VARCHAR"/>
        <result column="author" property="author" jdbcType="VARCHAR"/>
        <result column="pubdate" property="pubdate" jdbcType="VARCHAR"/>
        <result column="page_count" property="pageCount" jdbcType="INTEGER"/>
        <result column="thumbnail" property="thumbnail" jdbcType="VARCHAR"/>
        <result column="text" property="text" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="searchDoc" resultMap="DocsSliceSearchVOResultMap">
        SELECT
            docs."id" AS doc_id,
            CASE
                WHEN docs.title IS NOT NULL
                    AND docs.title != '' THEN
                    docs.title ELSE docs."name"
                END AS docs_name,
            author,
            pubdate,
            page_count,
            thumbnail,
            slice."content" AS "text",
            docs."content" AS url
        FROM
            aigc_docs_slice slice
                JOIN aigc_docs docs ON docs.deleted = 0
                AND docs."id" = slice.docs_id
        WHERE
            slice.deleted = 0
          AND slice."content" ILIKE concat ( '%', #{content}, '%' )
    </select>
</mapper>